import { of, throwError, Subject } from 'rxjs';
import { TitleCasePipe } from '@angular/common';
import { Router } from '@angular/router';

// Create a test class that mimics the ManageCasesComponent behavior
class ManageCasesComponentForTesting {
  private readonly destroy$ = new Subject<void>();

  caseStatusMap = new Map<string, string[]>();
  rowData: any[];
  caseStatuses: any[] = [];
  gridApi: any;
  errors: string[] = [];
  user: any;
  canEdit = false;
  path = location.pathname;
  actions = { EDIT: 'EDIT' };
  columnDefs: any;
  hierarchies: any[] = [];

  constructor(
    private userService: any,
    private editingStateService: any,
    private toastService: any,
    private dialogMessageService: any,
    private adminService: any,
    private titleCasePipe: any,
    private router: any,
    private organizationService: any
  ) {
    // Mock the editing state service subscription
    this.editingStateService.getValue = jest.fn().mockReturnValue(of('SAVE'));
  }

  ngOnInit(): void {
    this.loadData();
  }

  setColumnDefinition() {
    this.columnDefs = [
      {
        field: 'name',
        editable: true,
        minWidth: 200,
        cellEditor: 'agTextCellEditor',
        cellEditorParams: { maxLength: 25 }
      },
      {
        field: 'openedBy',
        headerName: 'Created By',
        minWidth: 200,
        valueGetter: (params: any) => `${params.data.statusModifiedBy.firstName} ${params.data.statusModifiedBy.lastName}`
      },
      {
        field: 'openedDate',
        headerName: 'Created Date',
        minWidth: 150,
        valueFormatter: (params: any) => params.value ? new Date(params.value).toLocaleDateString() : null
      },
      {
        field: 'description',
        editable: true,
        minWidth: 350,
        cellEditor: 'agTextCellEditor',
        cellEditorParams: { maxLength: 2047 }
      },
      {
        field: 'hierarchyData',
        headerName: 'Hierarchy',
        editable: true,
        minWidth: 250,
        cellClass: 'overflow-visible',
        valueFormatter: (params: any) => params.data?.hierarchyData?.label
      },
      {
        field: 'currentStatus',
        editable: (params: any) => !!params.data.caseId,
        minWidth: 150,
        cellClass: (params: any) => (!params.data.caseId ? 'not-editable' : ''),
        valueFormatter: (params: any) => this.titleCasePipe.transform(params.value)
      }
    ];
  }

  onCellAction(message: any) {
    switch (message.action) {
      case 'VIEW':
        this.onView(message);
        break;
    }
  }

  private onView(message: any) {
    this.router.navigate([window.location.pathname, message.rawData.name]);
  }

  onGridIsReady(gridApi: any) {
    this.gridApi = gridApi;
  }

  onNewRow(): any {
    return {
      caseId: undefined,
      name: '',
      openedBy: this.user,
      openedDate: new Date(),
      description: '',
      location: '',
      currentStatus: 'open',
      statusModifiedBy: this.user,
      statusUpdateDate: new Date()
    };
  }

  private loadData() {
    // Mock forkJoin behavior
    const mockResponse = {
      cases: [],
      caseStatusTransitions: [],
      user: { id: 1, name: 'Test User' },
      hierarchies: []
    };

    // Simulate the forkJoin call
    this.adminService.findUserCases().subscribe((cases: any) => {
      this.rowData = cases;
    });

    this.adminService.findAllCaseStatusTransitions().subscribe((transitions: any) => {
      if (transitions.length) {
        this.canEdit = true;
        transitions.forEach((ct: any) => {
          if (!this.caseStatusMap.has(ct.source)) {
            this.caseStatusMap.set(ct.source, [ct.source]);
            this.caseStatusMap.get(ct.source)!.push(ct.target);
          } else {
            this.caseStatusMap.get(ct.source)!.push(ct.target);
          }
        });
        transitions.forEach((ct: any) => {
          if (!this.caseStatusMap.has(ct.target)) {
            this.caseStatusMap.set(ct.target, [ct.target]);
          }
        });
      }
    });

    this.userService.findLoggedUserInfo().subscribe((user: any) => {
      this.user = user;
    });

    this.organizationService.findUserOrgTree().subscribe((hierarchies: any) => {
      this.hierarchies = hierarchies;
    });

    this.setColumnDefinition();
  }

  private saveData() {
    if (this.validateData()) {
      const data = this.getGridData();
      data.forEach((r: any) => {
        delete r?.hierarchyData?.children;
        delete r?.hierarchyData?.parent;
      });

      this.adminService.updateCases(data).subscribe(
        (resp: any) => {
          this.rowData = resp;
          this.toastService.displaySuccess('Cases saved successfully.');
        },
        () => this.toastService.displayError('Save cases failed.')
      );
    }
  }

  private getGridData() {
    // Mock implementation
    return this.rowData || [];
  }

  private validateData(): boolean {
    const data = this.getGridData();
    this.errors = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      if (!row.name) {
        this.errors.push(`${(this.errors.length + 1).toString()}. Cases Grid: Row ${i + 1}: Case Name is required.`);
      }
      if (!row.hierarchyData) {
        this.errors.push(`${(this.errors.length + 1).toString()}. Cases Grid: Row ${i + 1}: Hierarchy is required.`);
      }
      if (!row.currentStatus) {
        this.errors.push(`${(this.errors.length + 1).toString()}. Cases Grid: Row ${i + 1}: Current Status is required.`);
      }

      // Check for duplicates
      const duplicates = data.filter((r: any) => r.name?.length && r.name === row.name);
      if (duplicates.length > 1) {
        this.errors.push(`${(this.errors.length + 1).toString()}. Cases Grid: Row ${i + 1}: Case Name "${row.name}" is duplicated.`);
      }
    }

    if (this.errors.length) {
      this.dialogMessageService.displayError(this.errors.join('\n'), false);
      this.errors = [];
      return false;
    }

    return true;
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
