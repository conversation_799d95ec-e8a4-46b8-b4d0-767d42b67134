import { of, throwError, Subject } from 'rxjs';

// Mock TitleCasePipe
class MockTitleCasePipe {
  transform(value: string): string {
    if (!value) return value;
    return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  }
}

// Create a test class that mimics the StateTransitionComponent behavior
class StateTransitionComponentForTesting {
  private readonly destroy$ = new Subject<void>();
  readonly MAX_STATES = 8;
  readonly NODE_DIAMETER = 95;
  readonly WAIT_GRAPH_MS = 1000;

  update$: Subject<any> = new Subject();
  center$: Subject<any> = new Subject();
  zoomToFit$: Subject<any> = new Subject();
  zoomOk: boolean;
  canAdjust = true;

  colors = ['#BDE8A5', '#80BFFF', '#9FE7FF', '#F8C2DA', '#D6CDEA', '#FFCF9F', '#FCF8C6', '#AEBBC2'];

  nodes: any[] = [];
  links: any[] = [];

  fromItems: any[] = [];
  toItems: any[] = [];

  statesItems: any[] = [];
  newState: string;

  states: any[] = [];

  path = location.pathname;
  actions = { EDIT: 'EDIT' };

  constructor(
    private dialogMessageService: any,
    private toastService: any,
    private titleCasePipe: MockTitleCasePipe,
    private adminService: any,
    public editingStateService: any
  ) {
    this.editingStateService.getValue = jest.fn().mockReturnValue(of('SAVE'));
  }

  ngOnInit() {
    this.loadData();
  }

  ngAfterViewInit() {
    this.setGraphHeight();
  }

  private loadData() {
    const mockNodes = [
      { code: 'open', description: 'Open' },
      { code: 'close', description: 'Close' }
    ];
    const mockLinks = [
      { id: '1', source: 'open', target: 'close' }
    ];

    this.adminService.findAllCaseStatus().subscribe((nodes: any) => {
      this.adminService.findAllCaseStatusTransitions().subscribe((links: any) => {
        const states: any[] = [];

        nodes.forEach((n: any) => {
          const color = this.colors.pop();
          states.push({ id: n.code, name: n.description, color });
          this.nodes.push({ id: n.code, label: n.description, color, dimension: { width: this.NODE_DIAMETER } });
          this.fromItems.push({ code: n.code, name: n.description });
          this.toItems.push({ code: n.code, name: n.description });
          this.statesItems.push({ code: n.code, name: n.description });
        });
        this.states = states;
        this.links = links.map((l: any) => ({ ...l, id: `n${l.id}` }));

        this.adjustGraph(false);
      });
    });
  }

  adjustGraph(zoomChanged: boolean) {
    if (this.canAdjust) {
      this.zoomOk = false;
      setTimeout(() => {
        this.update$.next(true);
        this.center$.next(true);
        this.zoomToFit$.next(true);
        this.zoomOk = true;
      }, this.WAIT_GRAPH_MS);
    }

    if (zoomChanged && this.canAdjust) {
      this.canAdjust = false;
      setTimeout(() => {
        this.canAdjust = true;
      }, this.WAIT_GRAPH_MS);
    }
  }

  createNode(value: string) {
    const state = { id: value, name: this.titleCasePipe.transform(value), color: this.colors.pop() };
    const states = [...this.states];
    states.push(state);
    this.states = states;
    this.nodes.push({
      id: state.id,
      label: state.name.length > 8 ? state.name.substring(0, 8) : state.name,
      color: state.color,
      dimension: {
        width: this.NODE_DIAMETER
      }
    });
    this.fromItems.push({ code: state.id, name: state.name });
    this.update$.next(true);

    this.newState = null;
  }

  removeNode(value: string) {
    this.nodes = this.nodes.filter(n => n.id !== value);

    if (this.links.some(l => l.source === value)) {
      this.links = this.links.filter(l => l.source !== value);
    }
    if (this.links.some(l => l.target === value)) {
      this.links = this.links.filter(l => l.target !== value);
    }

    this.fromItems = this.fromItems.filter(i => i.code !== value);

    this.update$.next(true);
  }

  createState() {
    if (this.statesItems.length >= this.MAX_STATES) {
      this.dialogMessageService.displayError(`States cannot exceed the maximum of ${this.MAX_STATES}.`);
    } else if (this.statesItems.some((s: any) => s.code === this.newState.toLowerCase())) {
      this.dialogMessageService.displayError(`The state "${this.newState}" already exist.`);
    } else {
      this.statesItems.push({ code: this.newState.toLowerCase(), name: this.newState });
      this.createNode(this.newState);
    }
  }

  deleteState(selectedState: string) {
    const value = selectedState.toLowerCase();
    this.statesItems = this.statesItems.filter((n: any) => n.code !== value);
    this.removeNode(value);
    this.deleteTarget(value);
  }

  setGraphHeight() {
    // Mock implementation
  }

  selectFrom(value: any) {
    if (value !== null) {
      this.toItems = this.fromItems.filter(i => i.code !== value.toLowerCase());
    } else {
      this.toItems = [...this.fromItems];
    }
  }

  deleteTarget(value: string) {
    this.states = this.states.filter((n: any) => {
      if (n.id === value) {
        this.colors.push(n.color);
      }
      return n.id !== value;
    });
  }

  linkNodes(from: string, to: string) {
    if (this.links.some(l => l.source === from && l.target === to)) {
      this.toastService.displayError(`The link "${from} -> ${to}" already exist.`);
    } else {
      this.links.push({
        id: `n${this.idGen().toString()}`,
        source: from,
        target: to
      });
      this.update$.next(true);
      this.adjustGraph(false);
    }
  }

  unlinkNode(from: string, to: string) {
    if (this.links.some(l => l.source === from && l.target === to)) {
      if (this.links.length > 1) {
        this.links = this.links.filter(l => l.source !== from || l.target !== to);
      } else {
        this.links = [];
      }

      this.update$.next(true);
    } else {
      this.toastService.displayError(`The Link "${from} -> ${to}" doesn't exist.`);
    }
  }

  idGen(): number {
    if (this.links.length) {
      return Math.max(...this.links.map(l => +l.id.substring(1, l.id.length))) + 1;
    } else {
      return 1;
    }
  }

  private saveData() {
    this.newState = null;
    const statuses: any[] = [];
    const transitions: any[] = [];

    this.nodes.forEach(n => statuses.push({ code: n.id, description: n.label }));
    this.links.forEach(l => transitions.push({ id: l.id.replace('n', ''), source: l.source, target: l.target }));

    this.adminService.updateCaseStatus(statuses).subscribe(
      () => {
        this.toastService.displaySuccess('States updated successfully.');
        this.adminService.updateCaseStatusTransitions(transitions).subscribe(
          () => {
            this.editingStateService.setValue('SUBMIT');
            this.toastService.displaySuccess('Transitions updated successfully.');
          },
          () => this.toastService.displayError('Failed updating Transitions.')
        );
      },
      () => this.toastService.displayError('Failed updating States.')
    );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}

describe('StateTransitionComponent', () => {
  let component: StateTransitionComponentForTesting;
  let dialogMessageService: any;
  let toastService: any;
  let titleCasePipe: MockTitleCasePipe;
  let adminService: any;
  let editingStateService: any;

  const mockCaseStatuses = [
    { code: 'open', description: 'Open' },
    { code: 'close', description: 'Close' },
    { code: 'pending', description: 'Pending' }
  ];

  const mockTransitions = [
    { id: '1', source: 'open', target: 'pending' },
    { id: '2', source: 'pending', target: 'close' }
  ];

  beforeEach(() => {
    dialogMessageService = {
      displayError: jest.fn()
    };

    toastService = {
      displaySuccess: jest.fn(),
      displayError: jest.fn()
    };

    titleCasePipe = new MockTitleCasePipe();

    adminService = {
      findAllCaseStatus: jest.fn(),
      findAllCaseStatusTransitions: jest.fn(),
      updateCaseStatus: jest.fn(),
      updateCaseStatusTransitions: jest.fn()
    };

    editingStateService = {
      getValue: jest.fn(),
      setValue: jest.fn(),
      setData: jest.fn(),
      editingState: { isEditing: false }
    };

    component = new StateTransitionComponentForTesting(
      dialogMessageService,
      toastService,
      titleCasePipe,
      adminService,
      editingStateService
    );
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default properties', () => {
      expect(component.MAX_STATES).toBe(8);
      expect(component.NODE_DIAMETER).toBe(95);
      expect(component.WAIT_GRAPH_MS).toBe(1000);
      expect(component.canAdjust).toBe(true);
      expect(component.colors).toHaveLength(8);
      expect(component.nodes).toEqual([]);
      expect(component.links).toEqual([]);
    });

    it('should initialize subjects', () => {
      expect(component.update$).toBeInstanceOf(Subject);
      expect(component.center$).toBeInstanceOf(Subject);
      expect(component.zoomToFit$).toBeInstanceOf(Subject);
    });

    it('should call loadData on ngOnInit', () => {
      const loadDataSpy = jest.spyOn(component as any, 'loadData').mockImplementation(() => {});

      component.ngOnInit();

      expect(loadDataSpy).toHaveBeenCalled();
    });

    it('should call setGraphHeight on ngAfterViewInit', () => {
      const setGraphHeightSpy = jest.spyOn(component, 'setGraphHeight').mockImplementation(() => {});

      component.ngAfterViewInit();

      expect(setGraphHeightSpy).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    beforeEach(() => {
      adminService.findAllCaseStatus.mockReturnValue(of(mockCaseStatuses));
      adminService.findAllCaseStatusTransitions.mockReturnValue(of(mockTransitions));
    });

    it('should load case statuses and transitions successfully', () => {
      component.ngOnInit();

      expect(adminService.findAllCaseStatus).toHaveBeenCalled();
      expect(adminService.findAllCaseStatusTransitions).toHaveBeenCalled();
    });

    it('should populate nodes from case statuses', () => {
      component.ngOnInit();

      expect(component.nodes).toHaveLength(3);
      expect(component.nodes[0]).toEqual({
        id: 'open',
        label: 'Open',
        color: expect.any(String),
        dimension: { width: 95 }
      });
    });

    it('should populate states array', () => {
      component.ngOnInit();

      expect(component.states).toHaveLength(3);
      expect(component.states[0]).toEqual({
        id: 'open',
        name: 'Open',
        color: expect.any(String)
      });
    });

    it('should populate fromItems and toItems', () => {
      component.ngOnInit();

      expect(component.fromItems).toHaveLength(3);
      expect(component.toItems).toHaveLength(3);
      expect(component.statesItems).toHaveLength(3);
    });

    it('should process links with n prefix', () => {
      component.ngOnInit();

      expect(component.links).toHaveLength(2);
      expect(component.links[0].id).toBe('n1');
      expect(component.links[1].id).toBe('n2');
    });

    it('should handle error in case status loading', () => {
      adminService.findAllCaseStatus.mockReturnValue(throwError('Error loading statuses'));

      expect(() => component.ngOnInit()).not.toThrow();
    });

    it('should handle error in transitions loading', () => {
      adminService.findAllCaseStatusTransitions.mockReturnValue(throwError('Error loading transitions'));

      expect(() => component.ngOnInit()).not.toThrow();
    });
  });

  describe('Graph Adjustment', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should adjust graph when canAdjust is true', () => {
      const updateSpy = jest.spyOn(component.update$, 'next');
      const centerSpy = jest.spyOn(component.center$, 'next');
      const zoomSpy = jest.spyOn(component.zoomToFit$, 'next');

      component.adjustGraph(false);

      expect(component.zoomOk).toBe(false);

      jest.advanceTimersByTime(1000);

      expect(updateSpy).toHaveBeenCalledWith(true);
      expect(centerSpy).toHaveBeenCalledWith(true);
      expect(zoomSpy).toHaveBeenCalledWith(true);
      expect(component.zoomOk).toBe(true);
    });

    it('should prevent adjustment when canAdjust is false', () => {
      component.canAdjust = false;
      const updateSpy = jest.spyOn(component.update$, 'next');

      component.adjustGraph(false);

      jest.advanceTimersByTime(1000);

      expect(updateSpy).not.toHaveBeenCalled();
    });

    it('should temporarily disable canAdjust when zoomChanged is true', () => {
      component.adjustGraph(true);

      expect(component.canAdjust).toBe(false);

      jest.advanceTimersByTime(1000);

      expect(component.canAdjust).toBe(true);
    });
  });

  describe('Node Management', () => {
    beforeEach(() => {
      component.colors = ['#BDE8A5', '#80BFFF'];
      component.states = [];
      component.nodes = [];
      component.fromItems = [];
    });

    it('should create a new node', () => {
      const updateSpy = jest.spyOn(component.update$, 'next');

      component.createNode('test');

      expect(component.states).toHaveLength(1);
      expect(component.states[0]).toEqual({
        id: 'test',
        name: 'Test',
        color: '#80BFFF'
      });

      expect(component.nodes).toHaveLength(1);
      expect(component.nodes[0]).toEqual({
        id: 'test',
        label: 'Test',
        color: '#80BFFF',
        dimension: { width: 95 }
      });

      expect(component.fromItems).toHaveLength(1);
      expect(updateSpy).toHaveBeenCalledWith(true);
      expect(component.newState).toBeNull();
    });

    it('should remove a node and its links', () => {
      component.nodes = [
        { id: 'test1', label: 'Test1' },
        { id: 'test2', label: 'Test2' }
      ];
      component.links = [
        { id: 'n1', source: 'test1', target: 'test2' },
        { id: 'n2', source: 'test2', target: 'test1' }
      ];
      component.fromItems = [
        { code: 'test1', name: 'Test1' },
        { code: 'test2', name: 'Test2' }
      ];

      const updateSpy = jest.spyOn(component.update$, 'next');

      component.removeNode('test1');

      expect(component.nodes).toHaveLength(1);
      expect(component.nodes[0].id).toBe('test2');
      expect(component.links).toHaveLength(0);
      expect(component.fromItems).toHaveLength(1);
      expect(updateSpy).toHaveBeenCalledWith(true);
    });
  });

  describe('State Management', () => {
    beforeEach(() => {
      component.statesItems = [
        { code: 'open', name: 'Open' },
        { code: 'close', name: 'Close' }
      ];
      component.colors = ['#BDE8A5', '#80BFFF'];
    });

    it('should create a new state successfully', () => {
      component.newState = 'pending';
      const createNodeSpy = jest.spyOn(component, 'createNode').mockImplementation(() => {});

      component.createState();

      expect(component.statesItems).toHaveLength(3);
      expect(component.statesItems[2]).toEqual({ code: 'pending', name: 'pending' });
      expect(createNodeSpy).toHaveBeenCalledWith('pending');
    });

    it('should prevent creating state when maximum states reached', () => {
      component.statesItems = new Array(8).fill({ code: 'test', name: 'Test' });
      component.newState = 'newstate';

      component.createState();

      expect(dialogMessageService.displayError).toHaveBeenCalledWith('States cannot exceed the maximum of 8.');
    });

    it('should prevent creating duplicate state', () => {
      component.newState = 'Open';

      component.createState();

      expect(dialogMessageService.displayError).toHaveBeenCalledWith('The state "Open" already exist.');
    });

    it('should delete a state and clean up related data', () => {
      component.statesItems = [
        { code: 'open', name: 'Open' },
        { code: 'close', name: 'Close' },
        { code: 'pending', name: 'Pending' }
      ];
      const removeNodeSpy = jest.spyOn(component, 'removeNode').mockImplementation(() => {});
      const deleteTargetSpy = jest.spyOn(component, 'deleteTarget').mockImplementation(() => {});

      component.deleteState('pending');

      expect(component.statesItems).toHaveLength(2);
      expect(component.statesItems.find(s => s.code === 'pending')).toBeUndefined();
      expect(removeNodeSpy).toHaveBeenCalledWith('pending');
      expect(deleteTargetSpy).toHaveBeenCalledWith('pending');
    });
  });

  describe('Link Management', () => {
    beforeEach(() => {
      component.links = [
        { id: 'n1', source: 'open', target: 'close' }
      ];
    });

    it('should create a link between nodes', () => {
      const updateSpy = jest.spyOn(component.update$, 'next');
      const adjustGraphSpy = jest.spyOn(component, 'adjustGraph').mockImplementation(() => {});
      const idGenSpy = jest.spyOn(component, 'idGen').mockReturnValue(2);

      component.linkNodes('close', 'pending');

      expect(component.links).toHaveLength(2);
      expect(component.links[1]).toEqual({
        id: 'n2',
        source: 'close',
        target: 'pending'
      });
      expect(updateSpy).toHaveBeenCalledWith(true);
      expect(adjustGraphSpy).toHaveBeenCalledWith(false);
    });

    it('should prevent creating duplicate link', () => {
      component.linkNodes('open', 'close');

      expect(toastService.displayError).toHaveBeenCalledWith('The link "open -> close" already exist.');
      expect(component.links).toHaveLength(1);
    });

    it('should unlink nodes successfully', () => {
      const updateSpy = jest.spyOn(component.update$, 'next');

      component.unlinkNode('open', 'close');

      expect(component.links).toHaveLength(0);
      expect(updateSpy).toHaveBeenCalledWith(true);
    });

    it('should handle unlinking non-existent link', () => {
      component.unlinkNode('close', 'pending');

      expect(toastService.displayError).toHaveBeenCalledWith('The Link "close -> pending" doesn\'t exist.');
    });

    it('should handle unlinking when only one link exists', () => {
      const updateSpy = jest.spyOn(component.update$, 'next');

      component.unlinkNode('open', 'close');

      expect(component.links).toEqual([]);
      expect(updateSpy).toHaveBeenCalledWith(true);
    });
  });

  describe('ID Generation', () => {
    it('should generate ID 1 when no links exist', () => {
      component.links = [];

      const id = component.idGen();

      expect(id).toBe(1);
    });

    it('should generate next sequential ID', () => {
      component.links = [
        { id: 'n1', source: 'a', target: 'b' },
        { id: 'n3', source: 'b', target: 'c' },
        { id: 'n2', source: 'c', target: 'd' }
      ];

      const id = component.idGen();

      expect(id).toBe(4);
    });
  });

  describe('Utility Functions', () => {
    it('should filter toItems when selecting from', () => {
      component.fromItems = [
        { code: 'open', name: 'Open' },
        { code: 'close', name: 'Close' },
        { code: 'pending', name: 'Pending' }
      ];

      component.selectFrom('Open');

      expect(component.toItems).toHaveLength(2);
      expect(component.toItems.find(item => item.code === 'open')).toBeUndefined();
    });

    it('should handle null value in selectFrom', () => {
      component.fromItems = [
        { code: 'open', name: 'Open' },
        { code: 'close', name: 'Close' }
      ];

      component.selectFrom(null);

      // Should not modify toItems when value is null
      expect(component.toItems).toEqual(component.fromItems);
    });

    it('should delete target and return color to pool', () => {
      component.states = [
        { id: 'open', name: 'Open', color: '#BDE8A5' },
        { id: 'close', name: 'Close', color: '#80BFFF' }
      ];
      component.colors = ['#9FE7FF'];

      component.deleteTarget('open');

      expect(component.states).toHaveLength(1);
      expect(component.states[0].id).toBe('close');
      expect(component.colors).toContain('#BDE8A5');
    });
  });

  describe('Data Saving', () => {
    beforeEach(() => {
      component.nodes = [
        { id: 'open', label: 'Open' },
        { id: 'close', label: 'Close' }
      ];
      component.links = [
        { id: 'n1', source: 'open', target: 'close' }
      ];
      component.newState = 'test';
    });

    it('should save states and transitions successfully', () => {
      adminService.updateCaseStatus.mockReturnValue(of({}));
      adminService.updateCaseStatusTransitions.mockReturnValue(of({}));

      (component as any).saveData();

      expect(component.newState).toBeNull();
      expect(adminService.updateCaseStatus).toHaveBeenCalledWith([
        { code: 'open', description: 'Open' },
        { code: 'close', description: 'Close' }
      ]);
      expect(adminService.updateCaseStatusTransitions).toHaveBeenCalledWith([
        { id: '1', source: 'open', target: 'close' }
      ]);
      expect(toastService.displaySuccess).toHaveBeenCalledWith('States updated successfully.');
      expect(toastService.displaySuccess).toHaveBeenCalledWith('Transitions updated successfully.');
      expect(editingStateService.setValue).toHaveBeenCalledWith('SUBMIT');
    });

    it('should handle error when saving states', () => {
      adminService.updateCaseStatus.mockReturnValue(throwError('Save error'));

      (component as any).saveData();

      expect(toastService.displayError).toHaveBeenCalledWith('Failed updating States.');
    });

    it('should handle error when saving transitions', () => {
      adminService.updateCaseStatus.mockReturnValue(of({}));
      adminService.updateCaseStatusTransitions.mockReturnValue(throwError('Save error'));

      (component as any).saveData();

      expect(toastService.displaySuccess).toHaveBeenCalledWith('States updated successfully.');
      expect(toastService.displayError).toHaveBeenCalledWith('Failed updating Transitions.');
    });
  });

  describe('Component Lifecycle', () => {
    it('should complete destroy subject on ngOnDestroy', () => {
      const nextSpy = jest.spyOn(component['destroy$'], 'next');
      const completeSpy = jest.spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty nodes array in createNode', () => {
      component.colors = [];

      expect(() => component.createNode('test')).not.toThrow();
    });

    it('should handle empty links array in removeNode', () => {
      component.links = [];
      component.nodes = [{ id: 'test', label: 'Test' }];

      expect(() => component.removeNode('test')).not.toThrow();
      expect(component.nodes).toHaveLength(0);
    });

    it('should handle case-insensitive state creation', () => {
      component.statesItems = [{ code: 'open', name: 'Open' }];
      component.newState = 'OPEN';

      component.createState();

      expect(dialogMessageService.displayError).toHaveBeenCalledWith('The state "OPEN" already exist.');
    });

    it('should handle long state names in createNode', () => {
      const longName = 'VeryLongStateName';
      component.colors = ['#BDE8A5'];

      component.createNode(longName);

      expect(component.nodes[0].label).toBe('Verylong'); // Truncated to 8 characters
    });

    it('should handle multiple link removals', () => {
      component.links = [
        { id: 'n1', source: 'a', target: 'b' },
        { id: 'n2', source: 'a', target: 'c' },
        { id: 'n3', source: 'b', target: 'c' }
      ];

      component.removeNode('a');

      expect(component.links).toHaveLength(1);
      expect(component.links[0]).toEqual({ id: 'n3', source: 'b', target: 'c' });
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete workflow from load to save', () => {
      // Setup initial data
      adminService.findAllCaseStatus.mockReturnValue(of(mockCaseStatuses));
      adminService.findAllCaseStatusTransitions.mockReturnValue(of(mockTransitions));
      adminService.updateCaseStatus.mockReturnValue(of({}));
      adminService.updateCaseStatusTransitions.mockReturnValue(of({}));

      // Initialize component
      component.ngOnInit();

      // Verify data loaded
      expect(component.nodes).toHaveLength(3);
      expect(component.links).toHaveLength(2);

      // Add a new state
      component.newState = 'review';
      component.createState();

      // Verify state added
      expect(component.statesItems).toHaveLength(4);

      // Add a link
      component.linkNodes('pending', 'review');

      // Verify link added
      expect(component.links).toHaveLength(3);

      // Save data
      (component as any).saveData();

      // Verify save operations
      expect(adminService.updateCaseStatus).toHaveBeenCalled();
      expect(adminService.updateCaseStatusTransitions).toHaveBeenCalled();
      expect(toastService.displaySuccess).toHaveBeenCalledTimes(2);
    });

    it('should handle state deletion workflow', () => {
      // Setup initial data
      component.statesItems = [
        { code: 'open', name: 'Open' },
        { code: 'pending', name: 'Pending' },
        { code: 'close', name: 'Close' }
      ];
      component.nodes = [
        { id: 'open', label: 'Open' },
        { id: 'pending', label: 'Pending' },
        { id: 'close', label: 'Close' }
      ];
      component.links = [
        { id: 'n1', source: 'open', target: 'pending' },
        { id: 'n2', source: 'pending', target: 'close' }
      ];
      component.states = [
        { id: 'open', name: 'Open', color: '#BDE8A5' },
        { id: 'pending', name: 'Pending', color: '#80BFFF' },
        { id: 'close', name: 'Close', color: '#9FE7FF' }
      ];

      // Delete a state
      component.deleteState('pending');

      // Verify cleanup
      expect(component.statesItems).toHaveLength(2);
      expect(component.nodes).toHaveLength(2);
      expect(component.links).toHaveLength(0); // All links involving 'pending' removed
      expect(component.states).toHaveLength(2);
    });
  });
});