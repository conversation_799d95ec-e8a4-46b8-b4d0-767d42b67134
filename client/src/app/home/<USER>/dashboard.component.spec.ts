import { of, throwError } from 'rxjs';

// Mock formatDate function
function formatDate(date: any, format: string, locale: string): string {
  if (!date) return '';
  const d = new Date(date);
  if (format === 'yyyy-MM-dd') {
    return d.toISOString().split('T')[0];
  }
  if (format === 'MM/dd/yyyy HH:mm:ss') {
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
  }
  return d.toISOString();
}

// Mock DashboardComponent class
class DashboardComponent {
  constructor(
    adminService: any,
    messageService: any,
    toastService: any,
    notificationService: any,
    homeService: any,
    router: any,
    el: any
  ) {}

  ngOnInit() {}
  ngAfterViewChecked() {}
}

// Mock GridStack
class MockGridStack {
  update = jest.fn();
  removeWidget = jest.fn();
  addWidget = jest.fn();
  save = jest.fn().mockReturnValue({
    children: [
      { content: 'cases-status-history-chart', x: 0, y: 0, w: 12, h: 466 },
      { content: 'cases-current-status-chart', x: 6, y: 466, w: 3, h: 497 }
    ]
  });
  static init = jest.fn().mockReturnValue(new MockGridStack());
}

// Mock ElementRef
class MockElementRef {
  nativeElement = {
    querySelector: jest.fn(),
    includes: jest.fn()
  };
}

// Create a test class that mimics the DashboardComponent behavior
class DashboardComponentForTesting {
  casesStatusHistoryChart = new MockElementRef();
  casesCurrentStatusChart = new MockElementRef();
  workflowStatusChart = new MockElementRef();
  caseWorkflowHistoryChart = new MockElementRef();

  cases: any[] = [];
  years: number[] = [];
  selectedYear: number;
  workflowTypes: string[] = [];
  selectedWorkflowType: string;

  casesStatus: number[] = [];
  casesStatusLabels: string[] = [];

  workflowStatus: any = {};
  workflowStatusLabels = ['Pending', 'Completed'];

  readonly caseColors = ['#42A5F5', '#66BB6A', '#FFA726', '#BDE8A5', '#80BFFF', '#9FE7FF', '#F8C2DA', '#D6CDEA'];

  openedClosedData: any;
  workflowHistoryData: any;

  openedClosedChartOptions = {
    plugins: { legend: { labels: { color: '#495057' } } },
    scales: {
      x: { ticks: { color: '#495057' }, grid: { color: '#ebedef' } },
      y: { ticks: { color: '#495057' }, grid: { color: '#ebedef' } }
    }
  };

  workflowHistoryChartOptions = {
    plugins: { legend: { display: false } },
    scales: { y: { beginAtZero: true } }
  };

  casesStatusData: any;
  casesStatusOptions = {
    plugins: { legend: { labels: { color: '#495057' } } }
  };

  workflowStatusData: any;
  workflowChartOptions = {
    plugins: { legend: { labels: { color: '#495057' } } }
  };

  gridApi: any;
  notifications: any[] = [];
  columnDefs: any[];
  searchValue: string;

  statusHistoryDatasets = [];
  displayChartSelection = false;
  chartDisplayOptions = {
    casesStatusHistory: true,
    casesCurrentStatus: true,
    workflowStatus: true,
    caseWorkflowHistory: true
  };

  grid: any = new MockGridStack();
  private gridInitialized = false;
  userConfig: any;

  readonly DATE_FORMAT = 'yyyy-MM-dd';
  startDate: string = formatDate(new Date().setDate(new Date().getDate() - 100), this.DATE_FORMAT, 'en-US');
  endDate: string = formatDate(new Date().setDate(new Date().getDate() + 1), this.DATE_FORMAT, 'en-US');

  constructor(
    private adminService: any,
    private messageService: any,
    private toastService: any,
    private notificationService: any,
    private homeService: any,
    private router: any,
    private el: any
  ) {}

  ngOnInit(): void {}

  private reload() {
    this.columnDefs = undefined;
    this.ngOnInit();
  }

  private loadCasesChart() {
    this.openedClosedData = {
      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
      datasets: this.statusHistoryDatasets
    };
  }

  private loadData() {
    this.loadNotifications();
    this.loadCharts();
  }

  loadUserConfig() {
    this.homeService.findUserDashboardConfig().subscribe((resp: any) => {
      if (resp) {
        if (resp?.['casesStatusHistoryChart']) {
          this.chartDisplayOptions.casesStatusHistory = true;
          this.grid.update(this.casesStatusHistoryChart.nativeElement, resp['casesStatusHistoryChart']);
        } else {
          this.chartDisplayOptions.casesStatusHistory = false;
          this.grid.removeWidget(this.casesStatusHistoryChart.nativeElement, true);
        }
        if (resp?.['casesCurrentStatusChart']) {
          this.chartDisplayOptions.casesCurrentStatus = true;
          this.grid.update(this.casesCurrentStatusChart.nativeElement, resp['casesCurrentStatusChart']);
        } else {
          this.chartDisplayOptions.casesCurrentStatus = false;
          this.grid.removeWidget(this.casesCurrentStatusChart.nativeElement, true);
        }
        if (resp?.['workflowStatusChart']) {
          this.chartDisplayOptions.workflowStatus = true;
          this.grid.update(this.workflowStatusChart.nativeElement, resp['workflowStatusChart']);
        } else {
          this.chartDisplayOptions.workflowStatus = false;
          this.grid.removeWidget(this.workflowStatusChart.nativeElement, true);
        }
        if (resp?.['caseWorkflowHistoryChart']) {
          this.chartDisplayOptions.caseWorkflowHistory = true;
          this.grid.update(this.caseWorkflowHistoryChart.nativeElement, resp['caseWorkflowHistoryChart']);
        } else {
          this.chartDisplayOptions.caseWorkflowHistory = false;
          this.grid.removeWidget(this.caseWorkflowHistoryChart.nativeElement, true);
        }
      } else {
        this.chartDisplayOptions.casesStatusHistory = true;
        this.chartDisplayOptions.casesCurrentStatus = true;
        this.chartDisplayOptions.workflowStatus = true;
        this.chartDisplayOptions.caseWorkflowHistory = true;
      }
    });
  }

  private loadNotifications() {
    this.notificationService.findUserNotifications().subscribe(
      (resp: any) => {
        this.notifications = resp;
        this.setColumnDefs();
      },
      (error: any) => {
        this.notifications = [];
        this.toastService.displayError('Notifications could not be loaded.');
      }
    );
  }

  private loadCharts() {
    this.adminService.findAllCases().subscribe(
      (resp: any) => {
        this.cases = resp;
        this.getYears();
        this.loadCasesStatusData();
      },
      () => this.toastService.displayError('Error occurred trying to get the cases.')
    );

    this.homeService.findWorkflowChart().subscribe((resp: any) => {
      this.workflowStatus = resp;
      this.workflowTypes = Object.keys(resp);
      this.loadWorkflowStatusChart({ value: 'All' });
    });

    this.loadCaseWorkflowStatusChart();
  }

  loadCaseWorkflowStatusChart() {
    this.homeService
      .findCaseWorkflowStatusChart(this.startDate, this.endDate)
      .subscribe((resp: any) => this.loadCaseWorkflowHistoryChart(resp));
  }

  private getYears() {
    this.homeService.findCaseStatusYears().subscribe((resp: any) => {
      this.years = resp;
      const year = Math.max(...this.years);

      if (isFinite(year)) {
        this.loadCasesStatusHistoryData(year);
      } else {
        this.loadUserConfig();
      }
    });
  }

  onCheckChart(chart: string, checked: boolean) {
    if (checked) {
      switch (chart) {
        case 'Cases Status History':
          this.grid.addWidget(this.casesStatusHistoryChart.nativeElement, { h: 466, w: 12, x: 0, y: 0 });
          break;
        case 'Cases Current Status':
          this.grid.addWidget(this.casesCurrentStatusChart.nativeElement, { h: 497, w: 3, x: 6, y: 466 });
          break;
        case 'Workflow Status':
          this.grid.addWidget(this.workflowStatusChart.nativeElement, { h: 493, w: 3, x: 9, y: 466 });
          break;
        case 'Job Workflow history':
          this.grid.addWidget(this.caseWorkflowHistoryChart.nativeElement, { h: 505, w: 6, x: 0, y: 466 });
          break;
      }
    } else {
      switch (chart) {
        case 'Cases Status History':
          this.grid.removeWidget(this.casesStatusHistoryChart.nativeElement, true);
          break;
        case 'Cases Current Status':
          this.grid.removeWidget(this.casesCurrentStatusChart.nativeElement, true);
          break;
        case 'Workflow Status':
          this.grid.removeWidget(this.workflowStatusChart.nativeElement, true);
          break;
        case 'Job Workflow history':
          this.grid.removeWidget(this.caseWorkflowHistoryChart.nativeElement, true);
          break;
      }
    }
  }

  private loadCasesStatusHistoryData(year: number) {
    this.selectedYear = year;
    this.statusHistoryDatasets = [];
    this.homeService.findCaseStatusChart(year).subscribe((resp: any) => {
      let colors = [...this.caseColors];

      Object.entries(resp).forEach((value: any) => {
        this.statusHistoryDatasets.push({
          label: value[0],
          data: value[1],
          fill: false,
          borderColor: colors.pop(),
          tension: 0.4
        });
      });

      this.loadCasesChart();
      this.loadUserConfig();
    });
  }

  private loadCasesStatusData() {
    this.clearCasesStatus();

    const labels = new Set<string>();
    this.cases.forEach(c => labels.add(c.currentStatus));

    this.casesStatusLabels = Array.from(labels);

    this.casesStatusLabels.forEach((l, i) => {
      for (const c of this.cases) {
        if (c.currentStatus === l) {
          ++this.casesStatus[i];
        }
      }
    });

    this.loadCasesStatusChart();
  }

  private loadCasesStatusChart() {
    this.casesStatusData = {
      labels: this.casesStatusLabels,
      datasets: [
        {
          data: this.casesStatus,
          backgroundColor: this.caseColors,
          hoverBackgroundColor: this.caseColors
        }
      ]
    };
  }

  loadWorkflowStatusChart(event: any) {
    this.selectedWorkflowType = event.value;

    this.workflowStatusData = {
      labels: this.workflowStatusLabels,
      datasets: [
        {
          data: this.workflowStatus[event.value],
          backgroundColor: this.caseColors,
          hoverBackgroundColor: this.caseColors
        }
      ]
    };
  }

  loadCaseWorkflowHistoryChart(data: any) {
    this.workflowHistoryData = {
      labels: ['Pending', 'Rejected By Sales', 'Rejected By Operations', 'Rejected By CEO', 'Proposal Generated', 'Waiting Proposal Response', 'Proposal Won', 'Proposal Lost'],
      datasets: [
        {
          label: null,
          data: [data.pending, data.rejectedBySalesManagerReview, data.rejectedByOperationsReview, data.rejectedByCEOReview, data.proposalGenerated, data.waitingProposalResponse, data.proposalWon, data.proposalLost],
          backgroundColor: ['rgba(255, 99, 132, 0.2)', 'rgba(255, 159, 64, 0.2)', 'rgba(255, 205, 86, 0.2)', 'rgba(75, 192, 192, 0.2)', 'rgba(54, 162, 235, 0.2)', 'rgba(153, 102, 255, 0.2)', 'rgba(201, 203, 207, 0.2)', 'rgba(150, 99, 64, 0.2)'],
          borderColor: ['rgb(255, 99, 132)', 'rgb(255, 159, 64)', 'rgb(255, 205, 86)', 'rgb(75, 192, 192)', 'rgb(54, 162, 235)', 'rgb(153, 102, 255)', 'rgb(201, 203, 207)', 'rgb(150, 99, 64)'],
          borderWidth: 1
        }
      ]
    };
  }

  caseWorkflowHistoryDateChange() {
    this.loadCaseWorkflowStatusChart();
  }

  filterJob(event: any) {}

  private clearCasesStatus() {
    this.casesStatus = [0, 0, 0, 0, 0, 0, 0, 0];
  }

  onYearChange(event: any) {
    this.loadCasesStatusHistoryData(event.value);
  }

  onGridIsReady(gridApi: any) {
    this.gridApi = gridApi;
  }

  private setColumnDefs() {
    this.columnDefs = [
      {
        field: 'type',
        minWidth: 250,
        maxWidth: 250,
        valueGetter: (params: any) => params.data.type
      },
      {
        headerName: 'ReceivedAt',
        field: 'createdDate',
        minWidth: 200,
        maxWidth: 200,
        valueFormatter: (params: any) => {
          return params.value ? formatDate(params.value, 'MM/dd/yyyy HH:mm:ss', 'en-US') : null;
        }
      },
      {
        field: 'body',
        headerName: 'Content',
        minWidth: 350
      }
    ];
  }

  onCellAction(message: any) {
    switch (message.action) {
      case 'VIEW':
        this.onView(message);
        break;
      case 'DELETE':
        this.onDelete(message);
        break;
    }
  }

  private onView(message: any) {
    this.router.navigate([message.rawData.url]);
  }

  onDelete(message: any) {
    this.notificationService.deleteUserNotification(message.rawData.userId, message.rawData.id).subscribe(() => {
      this.toastService.displaySuccess('Notification deleted successfully.', 2000);
    });
  }

  displayChartSelectionOptions() {
    this.displayChartSelection = true;
  }

  saveUserCharts() {
    const gridstackData = this.grid.save(true, true).children;
    let data: any = {};

    gridstackData.forEach((c: any) => {
      if (c.content.includes('cases-status-history-chart')) {
        data.casesStatusHistoryChart = c;
        delete data.casesStatusHistoryChart.content;
      } else if (c.content.includes('cases-current-status-chart')) {
        data.casesCurrentStatusChart = c;
        delete data.casesCurrentStatusChart.content;
      } else if (c.content.includes('workflow-status-chart')) {
        data.workflowStatusChart = c;
        delete data.workflowStatusChart.content;
      } else if (c.content.includes('case-workflow-history-chart')) {
        data.caseWorkflowHistoryChart = c;
        delete data.caseWorkflowHistoryChart.content;
      }
    });

    this.homeService.updateUserDashboardConfig(data).subscribe(
      () => {
        this.toastService.displaySuccess('User dashboard configuration saved successfully.');
      },
      () => this.toastService.displayError('Error occurred while saving user dashboard configuration.')
    );

    this.displayChartSelection = false;
  }

  ngAfterViewChecked() {
    if (!this.gridInitialized && document.querySelector('.grid-stack')) {
      const options = {
        cellHeight: 1,
        oneColumnSize: 768,
        disableOneColumnMode: true,
        float: false,
        acceptWidgets: function (el: any) {
          return true;
        }
      };

      this.grid = MockGridStack.init(options);
      this.loadData();
      this.gridInitialized = true;
    }
  }
}

describe('DashboardComponent', () => {
  let component: DashboardComponentForTesting;
  let adminService: any;
  let messageService: any;
  let toastService: any;
  let notificationService: any;
  let homeService: any;
  let router: any;
  let el: any;

  const mockCases = [
    { currentStatus: 'Open', name: 'Case 1' },
    { currentStatus: 'Closed', name: 'Case 2' },
    { currentStatus: 'Open', name: 'Case 3' }
  ];

  const mockNotifications = [
    { type: 'INFO', createdDate: new Date(), body: 'Test notification 1', userId: 1, id: 1, url: '/test1' },
    { type: 'WARNING', createdDate: new Date(), body: 'Test notification 2', userId: 1, id: 2, url: '/test2' }
  ];

  const mockWorkflowChart = {
    'All': [10, 5],
    'Type1': [8, 3],
    'Type2': [2, 2]
  };

  const mockCaseWorkflowHistory = {
    pending: 5,
    rejectedBySalesManagerReview: 2,
    rejectedByOperationsReview: 1,
    rejectedByCEOReview: 0,
    proposalGenerated: 3,
    waitingProposalResponse: 4,
    proposalWon: 6,
    proposalLost: 2
  };

  beforeEach(() => {
    adminService = {
      findAllCases: jest.fn()
    };

    messageService = {
      add: jest.fn()
    };

    toastService = {
      displaySuccess: jest.fn(),
      displayError: jest.fn()
    };

    notificationService = {
      findUserNotifications: jest.fn(),
      deleteUserNotification: jest.fn()
    };

    homeService = {
      findUserDashboardConfig: jest.fn(),
      findCaseStatusChart: jest.fn(),
      findCaseWorkflowStatusChart: jest.fn(),
      findCaseStatusYears: jest.fn(),
      findWorkflowChart: jest.fn(),
      updateUserDashboardConfig: jest.fn()
    };

    router = {
      navigate: jest.fn()
    };

    el = new MockElementRef();

    component = new DashboardComponentForTesting(
      adminService,
      messageService,
      toastService,
      notificationService,
      homeService,
      router,
      el
    );

    // Setup default mock returns
    adminService.findAllCases.mockReturnValue(of(mockCases));
    notificationService.findUserNotifications.mockReturnValue(of(mockNotifications));
    homeService.findWorkflowChart.mockReturnValue(of(mockWorkflowChart));
    homeService.findCaseStatusYears.mockReturnValue(of([2022, 2023, 2024]));
    homeService.findCaseStatusChart.mockReturnValue(of({ 'Open': [1, 2, 3], 'Closed': [2, 1, 0] }));
    homeService.findCaseWorkflowStatusChart.mockReturnValue(of(mockCaseWorkflowHistory));
    homeService.findUserDashboardConfig.mockReturnValue(of(null));
    homeService.updateUserDashboardConfig.mockReturnValue(of({}));
    notificationService.deleteUserNotification.mockReturnValue(of({}));
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default properties', () => {
      expect(component.cases).toEqual([]);
      expect(component.years).toEqual([]);
      expect(component.workflowStatusLabels).toEqual(['Pending', 'Completed']);
      expect(component.caseColors).toHaveLength(8);
      expect(component.chartDisplayOptions.casesStatusHistory).toBe(true);
      expect(component.chartDisplayOptions.casesCurrentStatus).toBe(true);
      expect(component.chartDisplayOptions.workflowStatus).toBe(true);
      expect(component.chartDisplayOptions.caseWorkflowHistory).toBe(true);
    });

    it('should initialize date properties correctly', () => {
      expect(component.startDate).toBeDefined();
      expect(component.endDate).toBeDefined();
      expect(component.DATE_FORMAT).toBe('yyyy-MM-dd');
    });

    it('should call ngOnInit without errors', () => {
      const ngOnInitSpy = jest.spyOn(component, 'ngOnInit');
      component.ngOnInit();
      expect(ngOnInitSpy).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    it('should load notifications successfully', () => {
      component['loadNotifications']();

      expect(notificationService.findUserNotifications).toHaveBeenCalled();
      expect(component.notifications).toEqual(mockNotifications);
      expect(component.columnDefs).toBeDefined();
    });

    it('should handle notification loading error', () => {
      notificationService.findUserNotifications.mockReturnValue(throwError('Error loading notifications'));

      component['loadNotifications']();

      expect(component.notifications).toEqual([]);
      expect(toastService.displayError).toHaveBeenCalledWith('Notifications could not be loaded.');
    });

    it('should load charts successfully', () => {
      component['loadCharts']();

      expect(adminService.findAllCases).toHaveBeenCalled();
      expect(homeService.findWorkflowChart).toHaveBeenCalled();
    });

    it('should handle cases loading error', () => {
      adminService.findAllCases.mockReturnValue(throwError('Error loading cases'));

      component['loadCharts']();

      expect(toastService.displayError).toHaveBeenCalledWith('Error occurred trying to get the cases.');
    });

    it('should load workflow chart data', () => {
      component['loadCharts']();

      expect(component.workflowStatus).toEqual(mockWorkflowChart);
      expect(component.workflowTypes).toEqual(['All', 'Type1', 'Type2']);
    });
  });

  describe('Chart Management', () => {
    beforeEach(() => {
      component.cases = mockCases;
    });

    it('should load cases status data correctly', () => {
      component['loadCasesStatusData']();

      expect(component.casesStatusLabels).toContain('Open');
      expect(component.casesStatusLabels).toContain('Closed');
      expect(component.casesStatus).toBeDefined();
    });

    it('should clear cases status', () => {
      component['clearCasesStatus']();

      expect(component.casesStatus).toEqual([0, 0, 0, 0, 0, 0, 0, 0]);
    });

    it('should load cases status chart', () => {
      component.casesStatusLabels = ['Open', 'Closed'];
      component.casesStatus = [2, 1];

      component['loadCasesStatusChart']();

      expect(component.casesStatusData).toBeDefined();
      expect(component.casesStatusData.labels).toEqual(['Open', 'Closed']);
      expect(component.casesStatusData.datasets[0].data).toEqual([2, 1]);
    });

    it('should load workflow status chart', () => {
      component.workflowStatus = mockWorkflowChart;

      component.loadWorkflowStatusChart({ value: 'All' });

      expect(component.selectedWorkflowType).toBe('All');
      expect(component.workflowStatusData).toBeDefined();
      expect(component.workflowStatusData.datasets[0].data).toEqual([10, 5]);
    });

    it('should load case workflow history chart', () => {
      component.loadCaseWorkflowHistoryChart(mockCaseWorkflowHistory);

      expect(component.workflowHistoryData).toBeDefined();
      expect(component.workflowHistoryData.labels).toHaveLength(8);
      expect(component.workflowHistoryData.datasets[0].data).toEqual([5, 2, 1, 0, 3, 4, 6, 2]);
    });

    it('should handle year change', () => {
      const loadCasesStatusHistoryDataSpy = jest.spyOn(component as any, 'loadCasesStatusHistoryData').mockImplementation(() => {});

      component.onYearChange({ value: 2023 });

      expect(loadCasesStatusHistoryDataSpy).toHaveBeenCalledWith(2023);
    });

    it('should handle date change for case workflow history', () => {
      const loadCaseWorkflowStatusChartSpy = jest.spyOn(component, 'loadCaseWorkflowStatusChart').mockImplementation(() => {});

      component.caseWorkflowHistoryDateChange();

      expect(loadCaseWorkflowStatusChartSpy).toHaveBeenCalled();
    });
  });

  describe('Grid Management', () => {
    it('should add chart widgets when checked', () => {
      component.onCheckChart('Cases Status History', true);

      expect(component.grid.addWidget).toHaveBeenCalledWith(
        component.casesStatusHistoryChart.nativeElement,
        { h: 466, w: 12, x: 0, y: 0 }
      );
    });

    it('should remove chart widgets when unchecked', () => {
      component.onCheckChart('Cases Status History', false);

      expect(component.grid.removeWidget).toHaveBeenCalledWith(
        component.casesStatusHistoryChart.nativeElement,
        true
      );
    });

    it('should handle all chart types for adding', () => {
      const charts = ['Cases Status History', 'Cases Current Status', 'Workflow Status', 'Job Workflow history'];

      charts.forEach(chart => {
        component.onCheckChart(chart, true);
      });

      expect(component.grid.addWidget).toHaveBeenCalledTimes(4);
    });

    it('should handle all chart types for removing', () => {
      const charts = ['Cases Status History', 'Cases Current Status', 'Workflow Status', 'Job Workflow history'];

      charts.forEach(chart => {
        component.onCheckChart(chart, false);
      });

      expect(component.grid.removeWidget).toHaveBeenCalledTimes(4);
    });

    it('should save user charts configuration', () => {
      component.saveUserCharts();

      expect(component.grid.save).toHaveBeenCalledWith(true, true);
      expect(homeService.updateUserDashboardConfig).toHaveBeenCalled();
      expect(component.displayChartSelection).toBe(false);
    });

    it('should handle save configuration success', () => {
      component.saveUserCharts();

      expect(toastService.displaySuccess).toHaveBeenCalledWith('User dashboard configuration saved successfully.');
    });

    it('should handle save configuration error', () => {
      homeService.updateUserDashboardConfig.mockReturnValue(throwError('Save error'));

      component.saveUserCharts();

      expect(toastService.displayError).toHaveBeenCalledWith('Error occurred while saving user dashboard configuration.');
    });
  });

  describe('User Configuration', () => {
    it('should load user config with existing data', () => {
      const mockConfig = {
        casesStatusHistoryChart: { x: 0, y: 0, w: 12, h: 466 },
        casesCurrentStatusChart: { x: 6, y: 466, w: 3, h: 497 }
      };
      homeService.findUserDashboardConfig.mockReturnValue(of(mockConfig));

      component.loadUserConfig();

      expect(component.chartDisplayOptions.casesStatusHistory).toBe(true);
      expect(component.chartDisplayOptions.casesCurrentStatus).toBe(true);
      expect(component.grid.update).toHaveBeenCalledTimes(2);
    });

    it('should load default config when no user config exists', () => {
      homeService.findUserDashboardConfig.mockReturnValue(of(null));

      component.loadUserConfig();

      expect(component.chartDisplayOptions.casesStatusHistory).toBe(true);
      expect(component.chartDisplayOptions.casesCurrentStatus).toBe(true);
      expect(component.chartDisplayOptions.workflowStatus).toBe(true);
      expect(component.chartDisplayOptions.caseWorkflowHistory).toBe(true);
    });
  });

  describe('Notifications Management', () => {
    beforeEach(() => {
      component.notifications = mockNotifications;
    });

    it('should set column definitions', () => {
      component['setColumnDefs']();

      expect(component.columnDefs).toHaveLength(3);
      expect(component.columnDefs[0].field).toBe('type');
      expect(component.columnDefs[1].field).toBe('createdDate');
      expect(component.columnDefs[2].field).toBe('body');
    });

    it('should handle grid ready event', () => {
      const mockGridApi = { test: 'api' };

      component.onGridIsReady(mockGridApi);

      expect(component.gridApi).toBe(mockGridApi);
    });

    it('should handle cell action VIEW', () => {
      const message = {
        action: 'VIEW',
        rawData: { url: '/test-url' }
      };

      component.onCellAction(message);

      expect(router.navigate).toHaveBeenCalledWith(['/test-url']);
    });

    it('should handle cell action DELETE', () => {
      const message = {
        action: 'DELETE',
        rawData: { userId: 1, id: 1 }
      };

      component.onCellAction(message);

      expect(notificationService.deleteUserNotification).toHaveBeenCalledWith(1, 1);
      expect(toastService.displaySuccess).toHaveBeenCalledWith('Notification deleted successfully.', 2000);
    });

    it('should handle onDelete directly', () => {
      const message = {
        rawData: { userId: 1, id: 1 }
      };

      component.onDelete(message);

      expect(notificationService.deleteUserNotification).toHaveBeenCalledWith(1, 1);
      expect(toastService.displaySuccess).toHaveBeenCalledWith('Notification deleted successfully.', 2000);
    });
  });

  describe('UI Interactions', () => {
    it('should display chart selection options', () => {
      component.displayChartSelectionOptions();

      expect(component.displayChartSelection).toBe(true);
    });

    it('should handle filter job', () => {
      expect(() => component.filterJob({ test: 'event' })).not.toThrow();
    });

    it('should reload data', () => {
      const ngOnInitSpy = jest.spyOn(component, 'ngOnInit');

      component['reload']();

      expect(component.columnDefs).toBeUndefined();
      expect(ngOnInitSpy).toHaveBeenCalled();
    });
  });

  describe('Component Lifecycle', () => {
    it('should handle ngAfterViewChecked when grid not initialized', () => {
      component['gridInitialized'] = false;

      // Mock document.querySelector to return a truthy value
      const originalQuerySelector = document.querySelector;
      document.querySelector = jest.fn().mockReturnValue(document.createElement('div'));

      const loadDataSpy = jest.spyOn(component as any, 'loadData').mockImplementation(() => {});

      component.ngAfterViewChecked();

      expect(component['gridInitialized']).toBe(true);
      expect(loadDataSpy).toHaveBeenCalled();

      // Restore original querySelector
      document.querySelector = originalQuerySelector;
    });

    it('should not reinitialize grid when already initialized', () => {
      component['gridInitialized'] = true;
      const loadDataSpy = jest.spyOn(component as any, 'loadData').mockImplementation(() => {});

      component.ngAfterViewChecked();

      expect(loadDataSpy).not.toHaveBeenCalled();
    });
  });

  describe('Data Processing', () => {
    it('should get years and load data', () => {
      const loadCasesStatusHistoryDataSpy = jest.spyOn(component as any, 'loadCasesStatusHistoryData').mockImplementation(() => {});

      component['getYears']();

      expect(homeService.findCaseStatusYears).toHaveBeenCalled();
      expect(loadCasesStatusHistoryDataSpy).toHaveBeenCalledWith(2024); // Max year from mock
    });

    it('should handle empty years array', () => {
      homeService.findCaseStatusYears.mockReturnValue(of([]));
      const loadUserConfigSpy = jest.spyOn(component, 'loadUserConfig').mockImplementation(() => {});

      component['getYears']();

      expect(loadUserConfigSpy).toHaveBeenCalled();
    });

    it('should load cases status history data', () => {
      const loadCasesChartSpy = jest.spyOn(component as any, 'loadCasesChart').mockImplementation(() => {});
      const loadUserConfigSpy = jest.spyOn(component, 'loadUserConfig').mockImplementation(() => {});

      component['loadCasesStatusHistoryData'](2023);

      expect(component.selectedYear).toBe(2023);
      expect(homeService.findCaseStatusChart).toHaveBeenCalledWith(2023);
      expect(loadCasesChartSpy).toHaveBeenCalled();
      expect(loadUserConfigSpy).toHaveBeenCalled();
    });

    it('should load cases chart with datasets', () => {
      component.statusHistoryDatasets = [
        { label: 'Open', data: [1, 2, 3], fill: false, borderColor: '#42A5F5', tension: 0.4 }
      ];

      component['loadCasesChart']();

      expect(component.openedClosedData).toBeDefined();
      expect(component.openedClosedData.labels).toHaveLength(12); // 12 months
      expect(component.openedClosedData.datasets).toEqual(component.statusHistoryDatasets);
    });
  });

  describe('Error Handling', () => {
    it('should handle component creation gracefully', () => {
      expect(() => {
        const testComponent = new DashboardComponentForTesting(
          adminService,
          messageService,
          toastService,
          notificationService,
          homeService,
          router,
          el
        );
        testComponent.ngOnInit();
      }).not.toThrow();
    });

    it('should handle missing data gracefully', () => {
      component.cases = [];
      component.notifications = [];

      expect(() => {
        component['loadCasesStatusData']();
        component['setColumnDefs']();
      }).not.toThrow();
    });
  });

  describe('Integration with DashboardComponent', () => {
    it('should have DashboardComponent class defined', () => {
      expect(DashboardComponent).toBeDefined();
      expect(typeof DashboardComponent).toBe('function');
    });

    it('should be able to instantiate DashboardComponent', () => {
      expect(() => new DashboardComponent(
        adminService,
        messageService,
        toastService,
        notificationService,
        homeService,
        router,
        el
      )).not.toThrow();
    });
  });
});