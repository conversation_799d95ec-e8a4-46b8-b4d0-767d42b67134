import { of, throwError } from 'rxjs';
import { debounceTime, map } from 'rxjs/operators';

// Mock environment
const mockEnvironment = {
  apiUrl: 'http://localhost:8080/api'
};

// Mock jQuery
const mockJQuery = {
  attr: jest.fn().mockReturnThis(),
  last: jest.fn().mockReturnThis(),
  removeAttr: jest.fn().mockReturnThis(),
  addClass: jest.fn().mockReturnThis(),
  val: jest.fn().mockReturnValue(''),
  modal: jest.fn().mockReturnThis(),
  collapse: jest.fn().mockReturnThis(),
  show: jest.fn().mockReturnThis(),
  hide: jest.fn().mockReturnThis(),
  append: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  toast: jest.fn().mockReturnThis(),
  remove: jest.fn().mockReturnThis(),
  length: 1
};

// Mock global $ and document
global.$ = jest.fn(() => mockJQuery);
Object.assign(global.$, mockJQuery);

// Mock document methods
Object.defineProperty(document, 'getElementById', {
  value: jest.fn().mockReturnValue({
    files: [new File(['test'], 'test.txt', { type: 'text/plain' })],
    click: jest.fn()
  }),
  writable: true
});

Object.defineProperty(document, 'createElement', {
  value: jest.fn().mockReturnValue({
    href: '',
    download: '',
    click: jest.fn(),
    appendChild: jest.fn(),
    removeChild: jest.fn()
  }),
  writable: true
});

Object.defineProperty(document, 'execCommand', {
  value: jest.fn(),
  writable: true
});

// Mock window methods
Object.defineProperty(window, 'URL', {
  value: {
    createObjectURL: jest.fn().mockReturnValue('blob:url')
  },
  writable: true
});

Object.defineProperty(window, 'open', {
  value: jest.fn(),
  writable: true
});

Object.defineProperty(window, 'close', {
  value: jest.fn(),
  writable: true
});

Object.defineProperty(window, 'opener', {
  value: {
    postMessage: jest.fn()
  },
  writable: true
});

// Mock location
Object.defineProperty(window, 'location', {
  value: {
    host: 'localhost:4200',
    href: 'http://localhost:4200/#/file-browser'
  },
  writable: true
});

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn()
  },
  writable: true
});

// Mock confirm
Object.defineProperty(window, 'confirm', {
  value: jest.fn().mockReturnValue(true),
  writable: true
});

// Create a test class that mimics the FileBrowserComponent behavior
class FileBrowserComponentForTesting {
  docbase: string;
  contentRoot: string;
  dirs: any[] = [];
  files: any[] = [];
  filesCopy: any = [];
  breadcrumbs: any[] = [];
  selectedDir: any = {};
  alertMessage: string = '';
  alertSuccess: boolean = true;
  dirMap: any = {};
  appId: string;
  ckEditorFuncNum: string;
  filter: string = '';
  loading: boolean = false;
  loggedInUser: any;
  searchValue: any;
  searchOptions = [];

  search = (text$: any) =>
    text$.pipe(
      debounceTime(200),
      map((term: string) =>
        term === ''
          ? []
          : this.searchOptions.filter(v => v.name.toLowerCase().indexOf(term.toLowerCase()) > -1).slice(0, 10)
      )
    );

  formatter = (x: { name: string }) => x.name;

  constructor(
    private http: any,
    private router: any,
    private route: any
  ) {
    this.ckEditorFuncNum = route.snapshot.queryParamMap.get('CKEditorFuncNum');
    this.appId = route.snapshot.queryParamMap.get('appId');
  }

  ngOnInit() {
    this.loggedInUser = null;
    if (!this.ckEditorFuncNum) {
      this.loading = false;
      this.setContentRoot();
    } else if (sessionStorage.getItem('loggedInUser')) {
      this.router.navigate(['login'], {
        queryParams: {
          logout: 'false'
        }
      });
      return;
    } else {
      this.loggedInUser = JSON.parse(sessionStorage.getItem('loggedInUser'));
      this.setContentRoot();
    }
  }

  setContentRoot() {
    this.loading = true;
    this.http
      .get(mockEnvironment.apiUrl + '/library/getFileRoot', {
        responseType: 'text'
      })
      .subscribe((resp: string) => {
        this.docbase = resp;
        this.listFilesAndDirectories();
      });
  }

  getFilesMetaData(dir: string) {
    let dirUrl = mockEnvironment.apiUrl + '/library/getFilesMetaData?dir=' + dir;
    return this.http.get(dirUrl);
  }

  listFilesAndDirectories() {
    if (!this.contentRoot) this.contentRoot = this.docbase;
    let dirUrl =
      mockEnvironment.apiUrl + '/library/listDirectories?dir=' + (this.contentRoot != this.docbase ? this.contentRoot : '');
    let fileUrl =
      mockEnvironment.apiUrl + '/library/listFiles?dir=' + (this.contentRoot != this.docbase ? this.contentRoot : '');

    this.http.get(dirUrl).subscribe((resp: any[]) => {
      this.loading = false;
      if (resp.length > 0) {
        this.dirs = resp;
        this.breadcrumbs = [
          {
            path: this.contentRoot == this.docbase ? '' : this.docbase,
            name: this.contentRoot,
            pathSeparator: resp[0].pathSeparator
          }
        ];
        this.selectedDir = this.breadcrumbs[0];
        this.setDirMap(this.selectedDir);
        this.setBreadCrumbState();
      }
    });

    this.http.get(fileUrl).subscribe((resp: any[]) => {
      this.files = resp;
      this.filesCopy = resp;
    });
  }
