{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "cache": {"enabled": false}}, "version": 1, "newProjectRoot": "projects", "projects": {"jas-ui": {"projectType": "application", "schematics": {"@schematics/angular:application": {"strict": true}, "@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "jas", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"preserveSymlinks": true, "outputPath": "dist/jas-ui", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["node_modules/gridstack/dist/gridstack.min.css", "src/styles.scss", "./node_modules/@dorsey-plus/dorsey-core/scss/dorsey-core.scss", "./node_modules/font-awesome/css/font-awesome.css", "./node_modules/primeng/resources/themes/saga-blue/theme.css", "./node_modules/primeng/resources/primeng.min.css", "node_modules/primeflex/primeflex.css", "./node_modules/primeicons/primeicons.css"], "scripts": [], "allowedCommonJsDependencies": ["webcola", "dagre", "stompjs", "sockjs-client", "lodash"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "6mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": {"scripts": true, "styles": true, "hidden": false, "vendor": true}, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "jas-ui:build:production"}, "development": {"browserTarget": "jas-ui:build:development"}}, "defaultConfiguration": "development", "options": {"port": 4204, "proxyConfig": "./proxy.config.json", "ssl": true, "sslKey": "ssl/localhost.key", "sslCert": "ssl/localhost.crt"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "jas-ui:build"}}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "jest.config.js"}}}}}}