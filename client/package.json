{"name": "jas-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve -o", "start:ssl": "ng serve --ssl true --ssl-key ssl/localhost.key --ssl-cert ssl/localhost.crt", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "jest", "notest": "jest --passWithNoTests", "coverage": "jest --coverage", "test:watch": "jest --watch", "core": "npm i ../../core/client/packages/dorsey-plus-dorsey-core-1.1.14.tgz --force"}, "private": true, "dependencies": {"@angular/animations": "^16.2.12", "@angular/cdk": "^16.2.12", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.12", "@angular/core": "^16.2.12", "@angular/forms": "^16.2.12", "@angular/localize": "^16.2.12", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "@dorsey-plus/dorsey-core": "^1.2.1", "@fortawesome/angular-fontawesome": "^0.10.2", "@fortawesome/fontawesome-svg-core": "^6.1.1", "@fortawesome/free-regular-svg-icons": "^6.1.1", "@fortawesome/free-solid-svg-icons": "^6.1.1", "@ng-bootstrap/ng-bootstrap": "^15.1.2", "@popperjs/core": "^2.10.2", "@swimlane/ngx-graph": "^9.0.1", "ag-grid-angular": "^31.3.4", "ag-grid-community": "^31.3.4", "ag-grid-enterprise": "^31.3.4", "angular2-chartjs": "^0.5.1", "bootstrap": "^5.1.3", "chart.js": "^3.8.2", "chartjs-plugin-datalabels": "^2.0.0", "d3": "^7.8.2", "dayjs": "^1.11.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "gridstack": "^8.4.0", "lodash": "^4.17.21", "net": "^1.0.2", "ng2-pdf-viewer": "^9.1.0", "ngx-extended-pdf-viewer": "^21.1.0", "primeflex": "^3.3.0", "primeicons": "^7.0.0", "primeng": "^16.2.12", "randomcolor": "^0.6.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.13.3"}, "devDependencies": {"@angular-builders/jest": "^16.0.1", "@angular-devkit/build-angular": "^16.2.16", "@angular/cli": "^16.2.16", "@angular/compiler-cli": "^16.2.12", "@types/chart.js": "^2.9.37", "@types/file-saver": "^2.0.5", "@types/jest": "^29.5.14", "@types/node": "^18.19.71", "@types/randomcolor": "^0.5.6", "jest": "^29.7.0", "jest-preset-angular": "13.1.0", "ngx-stripe": "^16.4.0", "prettier": "^2.6.2", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "ts-jest": "^29.3.0", "typescript": "^5.1.6"}, "overrides": {"@swimlane/ngx-graph": {"@angular/common": "^16.0.0", "@angular/core": "^16.0.0", "@angular/animations": "^16.0.0"}}}