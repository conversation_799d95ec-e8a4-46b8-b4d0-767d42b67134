plugins {
    id 'org.springframework.boot' version '3.1.4'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'java'
    id "org.sonarqube" version "3.4.0.2513"
}

group = 'com.dorsey'
version = '0.0.1-SNAPSHOT'

java {
    sourceCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenLocal()
    mavenCentral()
    maven {
        name = 'GitLab'
        url = 'https://gitlab.dorseyplus.com/api/v4/groups/dorsey-plus/-/packages/maven'
        credentials(PasswordCredentials) {
            username = 'dorsey-core'
            password = '3CqaiANbszYVYfzvdk-q'
        }
        authentication {
            basic(BasicAuthentication)
        }
    }
    maven {
        url "https://build.shibboleth.net/nexus/content/repositories/releases/"
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    annotationProcessor 'org.projectlombok:lombok:1.18.24'
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    implementation 'org.springframework.data:spring-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.security:spring-security-saml2-service-provider:6.1.4'
//	implementation group: 'org.springframework.security.extensions', name: 'spring-security-saml2-core', version: '1.0.10.RELEASE'
    implementation 'org.springframework.boot:spring-boot-starter-web'
//	implementation 'org.springframework.boot:spring-boot-starter-tomcat'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.springframework.boot:spring-boot-devtools'
    implementation 'org.springframework.boot:spring-boot-starter-logging'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework:spring-aspects'
//	implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
    implementation 'com.dorsey:core:1.2.9'
    implementation 'jakarta.validation:jakarta.validation-api:3.0.2'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.3'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.13.3'
    implementation 'com.fasterxml.jackson.module:jackson-module-parameter-names:2.13.3'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.13.3'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-hibernate6:2.15.0-rc3'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.3'
    implementation 'io.hypersistence:hypersistence-utils-hibernate-60:3.5.3'
    implementation 'com.vladmihalcea:hibernate-types-60:2.21.1'
    implementation 'org.jxls:jxls-reader:2.0.6'
	implementation group: 'org.hibernate.orm', name: 'hibernate-core', version: '6.3.0.CR1'
    implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'
    implementation group: 'commons-io', name: 'commons-io', version: '2.14.0'
    implementation 'org.apache.commons:commons-text:1.11.0'
    implementation group: 'org.modelmapper', name: 'modelmapper', version: '3.0.0'
    implementation group: 'com.google.code.gson', name: 'gson', version: '2.10.1'
    compileOnly group: 'org.projectlombok', name: 'lombok', version: '1.18.24'
    compileOnly 'jakarta.platform:jakarta.jakartaee-web-api:10.0.0'
    implementation 'org.postgresql:postgresql:42.3.7'
    implementation 'mysql:mysql-connector-java:8.0.33'
//	runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.2'
//	runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.2'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'com.h2database:h2'
    testImplementation 'org.flywaydb:flyway-core'
}

jar {
    enabled = false
}

tasks.named('test') {
    useJUnitPlatform()
}
