# Overview
...

# Setup Local Environment

The following steps are for Windows 10/11 and Intellij IDE:

    1. Download and install Node Js .msi installer: https://nodejs.org/en/download
    2. Download and install Java SDK LTS .exe for windows: https://www.oracle.com/java/technologies/downloads/
    3. Download and install Git for Windows: https://git-scm.com/downloads/win
    4. Download and install Docker: https://docs.docker.com/desktop/setup/install/windows-install/
    5. Run command "npm install -g @angular/cli" in a terminal (CMD/Powershell) to install Angular CLI.
    6. Clone projects from gitlab. will need a ssh token.
    7. Open jas porject in Intellij. Go to "Run > Edit Configurations" and add a npm configuration with the following values:
        - package.json: Select jas package json.
        - Command: run
        - Scripts: start:ssl
    8. Install the following intellij plugin: Just (linux_china)
    9. Install Just:
        - Open Powershell.
        - Check execution policies by running command "Get-ExecutionPolicy".
        - If returns restricted run "Set-ExecutionPolicy AllSigned" or "Set-ExecutionPolicy Bypass -Scope Process".
        - Install Chocolatey by running command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))" 
        - Install Just by running command "choco install just"
    10. Open powershell as Administrator and run the following commands needed for Docker and then restart:
        - Enable-WindowsOptionalFeature -Online -FeatureName VirtualMachinePlatform -NoRestart
        - Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Windows-Subsystem-Linux -NoRestart
    11. 


# SAML

In order to start the application using the SAML authentication, you must enable de profile `saml`.

The client app must be run with https, locally you can run `npm start:ssl` to start it with https with a dummy certificate.

If you get an error screen about the invalid certificate in chrome, type `thisisunsafe` anywhere on the screen.
