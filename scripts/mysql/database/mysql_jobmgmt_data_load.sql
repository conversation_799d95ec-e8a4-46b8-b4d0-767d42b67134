INSERT INTO `system` (id, skin, theme, colors, logo) VALUES
('system-config', NULL, 'jay_blue', '#3A5976,#0086AD,#A9C3C5', NULL);

INSERT INTO hierarchy_level (level, hierarchy_name, description) VALUES
  (0, 'Organization', 'Organization Description.'),
  (1, 'Division', 'Division Description.'),
  (2, 'Facility', 'Facility Description.');

INSERT INTO hierarchy_node (node_id, parent_id, level, hierarchy_value) VALUES
  (0, NULL, 0, 'Department Of Justice'),
  (1, 0, 1, 'Eastern'),
  (2, 0, 1, 'Central'),
  (3, 0, 1, 'Western'),
  (4, 1, 2, 'A'),
  (5, 1, 2, 'B'),
  (6, 2, 2, 'C'),
  (7, 3, 2, 'D'),
  (8, 3, 2, 'E');

INSERT INTO case_status_transition (id, source, target) VALUES
  (1, 'open', 'close'),
  (2, 'close', 'open'),
  (3, 'open', 'cold'),
  (4, 'cold', 'open'),
  (5, 'close', 'archive');

INSERT INTO users (user_id, email, is_active, first_name, last_name, title, hierarchy_id, effective_date, termination_date) VALUES
(UUID_TO_BIN('5e16afd9-0b44-4e97-a1e8-3dcc1afa4beb'), '<EMAIL>', TRUE, 'Sam', 'Wooster', 'M.D.', 0, '2012-06-18', NULL),
(UUID_TO_BIN('9fdaed10-dcc2-45fd-98e7-677429b413d9'), '<EMAIL>', TRUE, 'Eliezer', 'Morillo', '', 0, '2012-06-18', NULL),
(UUID_TO_BIN('2d8b089a-5906-4da5-aa3c-2450a1a32530'), '<EMAIL>', TRUE, 'Dorsey', 'Test', 'M.D.', 0, '2012-06-18', NULL);

INSERT INTO role (role_id, name, department, superior_id, description) VALUES
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'Admin', 'Administration', null, 'System manager'),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'Sales Associate', 'Sales', null, 'Welcome customers on arrival at a retailer'),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'Sales Manager', 'Sales', null, 'Provides leadership to the sales team'),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'Operations Manager', 'Operations', null, 'Implement and maintain organization processes'),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'CEO', 'Administration', null, 'Manage company'),
(UUID_TO_BIN('76c90a96-10fa-4776-b1a2-0ba9887d2bf1'), 'Visitor', 'Unknown', null, 'Unknown');

INSERT INTO user_roles_xref (user_id, role_id) VALUES
(UUID_TO_BIN('5e16afd9-0b44-4e97-a1e8-3dcc1afa4beb'), UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd')),
(UUID_TO_BIN('9fdaed10-dcc2-45fd-98e7-677429b413d9'), UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd')),
(UUID_TO_BIN('2d8b089a-5906-4da5-aa3c-2450a1a32530'), UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'));

INSERT INTO role_capabilities_xref (role_id, component, view, edit, `create`, `delete`) VALUES
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,user-list', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,role-list', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,role-list,details', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,audit-log', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,demo', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,system', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,load-data', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,cases', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,cases,:case', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,manage-workflow', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,manage-workflow,:workflow', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,manage-workflow,:workflow,:task', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,organization', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,state-management', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'admin,state-management,jobs', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'home', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'home,dashboard', true, true, true, true),
(UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), 'home,workflow', true, true, true, true),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,user-list', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,role-list', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,role-list,details', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,audit-log', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,demo', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,system', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,load-data', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,cases', true, true, true, true),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,cases,:case', true, true, true, true),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,manage-workflow', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,manage-workflow,:workflow', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,manage-workflow,:workflow,:task', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,organization', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,state-management', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'admin,state-management,jobs', false, false, false, false),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'home', true, true, true, true),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'home,dashboard', true, true, true, true),
(UUID_TO_BIN('2a97fbe1-487e-4bce-9fc6-5581ce2508ad'), 'home,workflow', true, true, true, true),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,user-list', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,role-list', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,role-list,details', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,audit-log', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,demo', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,system', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,load-data', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,cases', true, true, true, true),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,cases,:case', true, true, true, true),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,manage-workflow', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,manage-workflow,:workflow', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,manage-workflow,:workflow,:task', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,organization', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,state-management', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'admin,state-management,jobs', false, false, false, false),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'home', true, true, true, true),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'home,dashboard', true, true, true, true),
(UUID_TO_BIN('038a99d7-acbf-4713-b17d-79ac55685453'), 'home,workflow', true, true, true, true),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,user-list', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,role-list', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,role-list,details', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,audit-log', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,demo', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,system', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,load-data', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,cases', true, true, true, true),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,cases,:case', true, true, true, true),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,manage-workflow', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,manage-workflow,:workflow', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,manage-workflow,:workflow,:task', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,organization', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,state-management', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'admin,state-management,jobs', false, false, false, false),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'home', true, true, true, true),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'home,dashboard', true, true, true, true),
(UUID_TO_BIN('7e33e8c1-**************-d877364c24b2'), 'home,workflow', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,user-list', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,role-list', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,role-list,details', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,audit-log', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,demo', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,system', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,load-data', false, false, false, false),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,cases', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,cases,:case', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,manage-workflow', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,manage-workflow,:workflow', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,manage-workflow,:workflow,:task', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,organization', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,state-management', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'admin,state-management,jobs', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'home', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'home,dashboard', true, true, true, true),
(UUID_TO_BIN('a3a1f14b-49fe-4d8f-b338-d800d9de75fe'), 'home,workflow', true, true, true, true);

INSERT INTO uploaded_data (data_set_code, file_name, status_code, uploaded_date) VALUES
  ( 'SMC', NULL, 'N', NULL),
  ( 'W', NULL, 'N', NULL),
  ( 'OH', NULL, 'N', NULL),
  ( 'C', NULL, 'N', NULL),
  ( 'R', NULL, 'N', NULL),
  ( 'U', NULL, 'N', NULL);

INSERT INTO workflow (id, workflow, description, created_by_system) VALUES
   (UUID_TO_BIN('6dff2664-3216-463c-9451-009791a41c68'), 'User Role Request', 'User Role Request', true),
   (UUID_TO_BIN('02d25f3b-5758-4e91-bc3d-c34eda3d4c4f'), 'User Hierarchy Request', 'User Hierarchy Request', true);

INSERT INTO workflow_task_type (task_id, workflow_id, task_name, service_level_agreement, service_level_agreement_warning, description) VALUES
  (UUID_TO_BIN('9aba5847-c986-4cd7-9037-e7ada1634673'), UUID_TO_BIN('6dff2664-3216-463c-9451-009791a41c68'), 'User Role Change', 3, 2, 'User Role Change'),
  (UUID_TO_BIN('da648d52-67a6-4937-9119-522abc09ef65'), UUID_TO_BIN('02d25f3b-5758-4e91-bc3d-c34eda3d4c4f'), 'User Hierarchy Change',  3, 2, 'User Hierarchy Change');

INSERT INTO role_workflow_task_xref (role_id, task_id) VALUES
  (UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), UUID_TO_BIN('9aba5847-c986-4cd7-9037-e7ada1634673')),
  (UUID_TO_BIN('d39091a9-fe2f-4952-9bb3-d760ad06a1bd'), UUID_TO_BIN('da648d52-67a6-4937-9119-522abc09ef65'));
