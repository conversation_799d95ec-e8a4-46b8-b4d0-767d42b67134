INSERT INTO roles_lov (code, description) VALUES
  ('OF', 'Officer'),
  ('AD', 'Admin'),
  ('DE', 'Detective'),
  ('PR', 'Prosecutor'),
  ('VS', 'Visitor');

INSERT INTO case_status_lov (code, description, deprecated) VALUES
  ('archive', 'Archive', false),
  ('close', 'Close', false),
  ('cold', 'Cold', false),
  ('open', 'Open', false);


INSERT INTO audit_action_lov (code, description) VALUES
  ('Delete', 'Delete'),
  ('Update', 'Update'),
  ('Create', 'Create'),
  ('View', 'View'),
  ('Login', 'Login'),
  ('Logout', 'Logout'),
  ('Upload', 'Upload');

INSERT INTO audit_entity_lov (code, description) VALUES
  ('Page', 'Page Name'),
  ('Group', 'User Group');

INSERT INTO notification_type_lov (code, description) VALUES
  ('CSC', 'Case Status Change');

INSERT INTO system_skin_lov (code, description) VALUES
  (1, 'System skin number #1'),
  (2, 'System skin number #2');

INSERT INTO system_theme_lov (code, description) VALUES
  ('jay_blue', 'Jay blue theme'),
  ('teal_grey', 'Teal grey theme'),
  ('persian', 'Persian theme'),
  ('custom', 'Custom theme');

INSERT INTO data_set_lov (code, display_order, description, java_class_name) VALUES
  ('LOGO', 0, 'Organization logo', NULL),
  ('OH', 1, 'Organization Hierarchy', NULL),
  ('SMC', 2, 'State Machine Jobs', NULL),
  ('C', 3, 'Jobs List', NULL),
  ('R', 4, 'Roles List', NULL),
  ('U', 5, 'Users List', NULL),
  ('W', 6, 'Workflows', NULL);

INSERT INTO uploaded_data_status_lov (code, description) VALUES
  ('U', 'Uploaded'),
  ('L', 'Locked'),
  ('N', 'None - Does Not Exist'),
  ('D', 'Deleted'),
  ('R', 'Reload');

INSERT INTO task_type_lov (code, data, description) VALUES
  ('JIF', '[{"field":"region","label":"Region","isRequired":false,"isNumeric":false,"fieldLength":100},{"field":"contactType","label":"Contact Type","values":["contact type 1","contact type 2","contact type 3"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"contractApproval","label":"Contract Approval","values":["No","Yes"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"contract","label":"GSA or MAC Contract","values":["GSA","MAC"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"omnia","label":"OMNIA","values":["No","Yes"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"marginOverrideRequested","label":"Margin Override Requested","values":["No","Yes"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"taxExempt","label":"Tax Exempt","values":["No","Yes"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"prevailingWage","label":"Prevailing Wage","isRequired":true,"isNumeric":true},{"field":"credit","label":"Credit","isRequired":true,"isNumeric":true},{"field":"bond","label":"Bond","isRequired":true,"isNumeric":true},{"field":"engineering","label":"Engineering","values":["No","Yes"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"badgingRequired","label":"Badging Required","values":["No","Yes"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"city","label":"City of Site","isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"operationReview","label":"Operation Review","values":["approved","rejected"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"estimatedCost","label":"Estimated Cost","isRequired":true,"isNumeric":true},{"field":"caseId","label":"Case Id","isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"proposalSent","label":"Proposal Sent","values":["No","Yes"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"bidOutcome","label":"Bid Outcome","values":["Won","Lost"],"isRequired":true,"isNumeric":false,"fieldLength":100},{"field":"proposalSentDate","label":"Proposal Sent Date","isRequired":true,"isNumeric":false,"fieldLength":100}]','Job Input Form');

INSERT INTO system_files_lov (code, description, file) VALUES
  ('OH', 'Organization Hierarchy Template', '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'),
  ('SMC', 'State Machine Jobs Template', '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'),
  ('W', 'Workflows Template', '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'),
  ('C', 'Jobs List Template', '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'),
  ('R', 'Roles List Template', '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'),
  ('U', 'Users List Template', '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');

INSERT INTO demo_lov (code, description) VALUES
  ('D', 'Default'),
  ('D1', 'Demo #1');
