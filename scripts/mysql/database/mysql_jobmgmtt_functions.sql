CREATE OR REPLACE FUNCTION d1()
  RETURNS void
  LANGUAGE plpgsql
AS
$$
BEGIN
  -- Clear data
  PERFORM default_data();

  --Config
  SET datestyle = "ISO, DMY";

  -- Create Users
  INSERT INTO users VALUES ('12a755f6-cd57-4cc9-8760-fe8845221244', 'l<PERSON><EMAIL>', true, '<PERSON><PERSON>', '<PERSON>', null, 'T.D.', 1, '2012-06-18 00:00:00', null),
                           ('3b4cc01e-76b0-4c28-90b6-361bff853d30', '<EMAIL>', true, '<PERSON>', '<PERSON>', null, 'T.D.', 2, '2012-06-18 00:00:00', null),
                           ('d1b6c829-59f7-4682-b8ca-08f240c00e7a', '<EMAIL>', true, '<PERSON>', '<PERSON>', null, 'S.D.', 3, '2012-06-18 00:00:00', null),
                           ('56a96128-a1be-4046-a7a5-8954adc8eb84', '<EMAIL>', true, 'Ander', '<PERSON><PERSON>', null, 'T.D.', 0, '2012-06-18 00:00:00', null),
                           ('8d55ac74-d71f-47ec-8e80-17ead7709a5c', '<EMAIL>', true, 'Wells', 'Daniels', null, 'S.D.', 2, '2012-06-18 00:00:00', null),
                           ('e8201944-dfd5-4dc2-850f-5827959ada60', '<EMAIL>', true, 'Brooke', 'Adkins', null, 'T.D.', 3, '2012-06-18 00:00:00', null);

  INSERT INTO user_roles_xref VALUES ('12a755f6-cd57-4cc9-8760-fe8845221244', '2a97fbe1-487e-4bce-9fc6-5581ce2508ad'),
                                     ('3b4cc01e-76b0-4c28-90b6-361bff853d30', '038a99d7-acbf-4713-b17d-79ac55685453'),
                                     ('d1b6c829-59f7-4682-b8ca-08f240c00e7a', '7e33e8c1-9488-4535-9061-d877364c24b2'),
                                     ('56a96128-a1be-4046-a7a5-8954adc8eb84', 'a3a1f14b-49fe-4d8f-b338-d800d9de75fe'),
                                     ('8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2a97fbe1-487e-4bce-9fc6-5581ce2508ad'),
                                     ('e8201944-dfd5-4dc2-850f-5827959ada60', '2a97fbe1-487e-4bce-9fc6-5581ce2508ad');

  -- Default Workflows and TaskTypes
  INSERT INTO workflow (id, workflow, description, created_by_system) VALUES ('6dff2664-3216-463c-9451-009791a41c68', 'User Role Request', 'User Role Request', true),
                                                                             ('02d25f3b-5758-4e91-bc3d-c34eda3d4c4f', 'User Hierarchy Request', 'User Hierarchy Request', true);

  INSERT INTO workflow_task_type (task_id, workflow_id, task_name, service_level_agreement, service_level_agreement_warning, description) VALUES ('9aba5847-c986-4cd7-9037-e7ada1634673', '6dff2664-3216-463c-9451-009791a41c68', 'User Role Change', 3, 2, 'User Role Change'),
                                                                                                                                                 ('da648d52-67a6-4937-9119-522abc09ef65', '02d25f3b-5758-4e91-bc3d-c34eda3d4c4f', 'User Hierarchy Change',  3, 2, 'User Hierarchy Change');
  INSERT INTO role_workflow_task_xref (role_id, task_id) VALUES ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', '9aba5847-c986-4cd7-9037-e7ada1634673'),
                                                                ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'da648d52-67a6-4937-9119-522abc09ef65');
  -- Create Workflow
  INSERT INTO workflow VALUES ('63620674-53bb-4fd6-86b0-713aa70f302e', 'Approval to Propose', 'No description', false, true, false);

  --Workflow TaskTypes
  INSERT INTO workflow_task_type VALUES ('1234adec-f678-4d3c-9c60-de6f4311f89e','63620674-53bb-4fd6-86b0-713aa70f302e','JIF','Awaiting Bid Results','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('3e5277d2-2a9c-4685-ba17-7efc6db8f19c','63620674-53bb-4fd6-86b0-713aa70f302e','JIF','Bid Lost','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('46833a75-74dd-481e-9740-5c1eb7e7187f','63620674-53bb-4fd6-86b0-713aa70f302e',NULL,'Start',NULL,NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('54c7a131-835d-4090-a348-64f1e1bda279','63620674-53bb-4fd6-86b0-713aa70f302e','JIF','Estimate Review','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('55155713-251d-43bc-aee5-6c562cf36189','63620674-53bb-4fd6-86b0-713aa70f302e',NULL,'End',NULL,NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('66208b54-fd31-4c84-ac95-7d16e04057ce','63620674-53bb-4fd6-86b0-713aa70f302e','JIF','Rejected','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('71dfe89b-11c3-4b85-8f46-3bec932a49c6','63620674-53bb-4fd6-86b0-713aa70f302e','JIF','Operations Review','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('99464e41-c3e0-46a6-94e1-552e7077fa2e','63620674-53bb-4fd6-86b0-713aa70f302e','JIF','Sales Manager Review','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('c1702bc3-9e3b-417c-8d89-828c540f608a','63620674-53bb-4fd6-86b0-713aa70f302e','JIF','Bid Won','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('cba0ddeb-4372-47f8-a75d-5c1bb0605e04','63620674-53bb-4fd6-86b0-713aa70f302e','JIF','CEO Review','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,FALSE,3,2),
                                        ('f8a27610-689e-447b-bb97-a9950429cd5e','63620674-53bb-4fd6-86b0-713aa70f302e','JIF','Prepare RFQ-RFP Response','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',NULL,FALSE,NULL,NULL,NULL,FALSE,NULL,TRUE,3,2);

  --Workflow TaskType Relations
  INSERT INTO workflow_task_type_relation VALUES (1,'63620674-53bb-4fd6-86b0-713aa70f302e','46833a75-74dd-481e-9740-5c1eb7e7187f','54c7a131-835d-4090-a348-64f1e1bda279',0,NULL,NULL,NULL),
                                                  (2,'63620674-53bb-4fd6-86b0-713aa70f302e','54c7a131-835d-4090-a348-64f1e1bda279','99464e41-c3e0-46a6-94e1-552e7077fa2e',1,NULL,NULL,NULL),
                                                  (3,'63620674-53bb-4fd6-86b0-713aa70f302e','99464e41-c3e0-46a6-94e1-552e7077fa2e','71dfe89b-11c3-4b85-8f46-3bec932a49c6',2,'operationReview','approved',NULL),
                                                  (4,'63620674-53bb-4fd6-86b0-713aa70f302e','99464e41-c3e0-46a6-94e1-552e7077fa2e','66208b54-fd31-4c84-ac95-7d16e04057ce',3,NULL,NULL,NULL),
                                                  (5,'63620674-53bb-4fd6-86b0-713aa70f302e','71dfe89b-11c3-4b85-8f46-3bec932a49c6','cba0ddeb-4372-47f8-a75d-5c1bb0605e04',4,'estimatedCost',25000,'>'),
                                                  (6,'63620674-53bb-4fd6-86b0-713aa70f302e','71dfe89b-11c3-4b85-8f46-3bec932a49c6','f8a27610-689e-447b-bb97-a9950429cd5e',5,'operationReview','approved',NULL),
                                                  (7,'63620674-53bb-4fd6-86b0-713aa70f302e','71dfe89b-11c3-4b85-8f46-3bec932a49c6','66208b54-fd31-4c84-ac95-7d16e04057ce',6,NULL,NULL,NULL),
                                                  (8,'63620674-53bb-4fd6-86b0-713aa70f302e','cba0ddeb-4372-47f8-a75d-5c1bb0605e04','f8a27610-689e-447b-bb97-a9950429cd5e',7,'operationReview','approved',NULL),
                                                  (9,'63620674-53bb-4fd6-86b0-713aa70f302e','cba0ddeb-4372-47f8-a75d-5c1bb0605e04','66208b54-fd31-4c84-ac95-7d16e04057ce',8,NULL,NULL,NULL),
                                                  (10,'63620674-53bb-4fd6-86b0-713aa70f302e','f8a27610-689e-447b-bb97-a9950429cd5e','1234adec-f678-4d3c-9c60-de6f4311f89e',9,NULL,NULL,NULL),
                                                  (11,'63620674-53bb-4fd6-86b0-713aa70f302e','1234adec-f678-4d3c-9c60-de6f4311f89e','c1702bc3-9e3b-417c-8d89-828c540f608a',10,'operationReview','approved',NULL),
                                                  (12,'63620674-53bb-4fd6-86b0-713aa70f302e','1234adec-f678-4d3c-9c60-de6f4311f89e','3e5277d2-2a9c-4685-ba17-7efc6db8f19c',11,NULL,NULL,NULL),
                                                  (13,'63620674-53bb-4fd6-86b0-713aa70f302e','3e5277d2-2a9c-4685-ba17-7efc6db8f19c','55155713-251d-43bc-aee5-6c562cf36189',12,NULL,NULL,NULL),
                                                  (14,'63620674-53bb-4fd6-86b0-713aa70f302e','c1702bc3-9e3b-417c-8d89-828c540f608a','55155713-251d-43bc-aee5-6c562cf36189',13,NULL,NULL,NULL),
                                                  (15,'63620674-53bb-4fd6-86b0-713aa70f302e','66208b54-fd31-4c84-ac95-7d16e04057ce','55155713-251d-43bc-aee5-6c562cf36189',14,NULL,NULL,NULL);

  --Workflow Role Task Xref
  INSERT INTO role_workflow_task_xref VALUES ('038a99d7-acbf-4713-b17d-79ac55685453','66208b54-fd31-4c84-ac95-7d16e04057ce'),
  ('038a99d7-acbf-4713-b17d-79ac55685453','3e5277d2-2a9c-4685-ba17-7efc6db8f19c'),
  ('038a99d7-acbf-4713-b17d-79ac55685453','1234adec-f678-4d3c-9c60-de6f4311f89e'),
  ('038a99d7-acbf-4713-b17d-79ac55685453','f8a27610-689e-447b-bb97-a9950429cd5e'),
  ('038a99d7-acbf-4713-b17d-79ac55685453','99464e41-c3e0-46a6-94e1-552e7077fa2e'),
  ('038a99d7-acbf-4713-b17d-79ac55685453','c1702bc3-9e3b-417c-8d89-828c540f608a'),
  ('2a97fbe1-487e-4bce-9fc6-5581ce2508ad','54c7a131-835d-4090-a348-64f1e1bda279'),
  ('2a97fbe1-487e-4bce-9fc6-5581ce2508ad','f8a27610-689e-447b-bb97-a9950429cd5e'),
  ('2a97fbe1-487e-4bce-9fc6-5581ce2508ad','66208b54-fd31-4c84-ac95-7d16e04057ce'),
  ('2a97fbe1-487e-4bce-9fc6-5581ce2508ad','3e5277d2-2a9c-4685-ba17-7efc6db8f19c'),
  ('2a97fbe1-487e-4bce-9fc6-5581ce2508ad','1234adec-f678-4d3c-9c60-de6f4311f89e'),
  ('2a97fbe1-487e-4bce-9fc6-5581ce2508ad','c1702bc3-9e3b-417c-8d89-828c540f608a'),
  ('7e33e8c1-9488-4535-9061-d877364c24b2','71dfe89b-11c3-4b85-8f46-3bec932a49c6'),
  ('a3a1f14b-49fe-4d8f-b338-d800d9de75fe','cba0ddeb-4372-47f8-a75d-5c1bb0605e04'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd','f8a27610-689e-447b-bb97-a9950429cd5e'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd','cba0ddeb-4372-47f8-a75d-5c1bb0605e04'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd','c1702bc3-9e3b-417c-8d89-828c540f608a'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd','99464e41-c3e0-46a6-94e1-552e7077fa2e'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd','71dfe89b-11c3-4b85-8f46-3bec932a49c6'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd','66208b54-fd31-4c84-ac95-7d16e04057ce'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd','54c7a131-835d-4090-a348-64f1e1bda279'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd','3e5277d2-2a9c-4685-ba17-7efc6db8f19c'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd','1234adec-f678-4d3c-9c60-de6f4311f89e');

  -- Insert Jobs
  INSERT INTO case_data_information VALUES ('6b9f211e-c70c-42d2-b663-a236313526cd', 'region 1', 'contact type 1', 'No', 'GSA', 'Yes', 'No', 'Yes', 234, 643, 744, 'No', 'Yes', 'city 1', 'approved', 15000, NULL, 'No', NULL),
                                           ('9d51bd7f-d523-49dc-a974-b377b8a21c26', 'region 2', 'contact type 2', 'No', 'MAC', 'No', 'Yes', 'Yes', 6345, 5345, 74, 'Yes', 'No', 'city 2', 'approved', 21000, NULL, 'No', NULL),
                                           ('6d8600aa-91f9-47fc-a52c-9e1ef503c418', 'region 3', 'contact type 3', 'No', 'MAC', 'Yes', 'Yes', 'No', 645, 242, 5236, 'No', 'Yes', 'city 3', 'rejected', 5985, NULL, 'No', NULL),
                                           ('b9f0cd00-c8b4-4ad3-8119-79bb934592ee', 'region 4', 'contact type 1', 'No', 'GSA', 'Yes', 'Yes', 'No', 784, 3635, 7456, 'No', 'Yes', 'city 4', 'rejected', 9885, NULL, 'No', NULL),
                                           ('c805fe68-a656-4b74-9a35-2779148863c8', 'region 5', 'contact type 2', 'No', 'GSA', 'Yes', 'No', 'Yes', 433, 2314, 996, 'No', 'Yes', 'city 5', 'approved', 23665, NULL, 'No', NULL),
                                           ('926bdf7e-c6e4-460b-b33a-28c5bb890a44', 'region 6', 'contact type 3', 'Yes', 'MAC', 'Yes', 'No', 'No', 78, 2452, 6454, 'No', 'No', 'city 6', 'approved', 598, NULL, 'No', NULL),
                                           ('afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f', 'region 7', 'contact type 1', 'No', 'GSA', 'Yes', 'No', 'Yes', 68, 3634, 2434, 'Yes', 'Yes', 'city 7', 'approved', 29365, NULL, 'No', '2023-10-15'),
                                           ('2f3e2095-4aff-440f-bb75-a98097322d88', 'region 8', 'contact type 2', 'No', 'GSA', 'No', 'No', 'No', 364, 454, 6556, 'Yes', 'Yes', 'city 8', 'approved', 2658, NULL, 'No', '2023-10-15'),
                                           ('30bc8170-1377-419d-ae2a-89057d91e842', 'region 9', 'contact type 3', 'No', 'GSA', 'Yes', 'No', 'No', 535, 746, 7456, 'No', 'Yes', 'city 9', 'rejected', 18698, NULL, 'No', '2023-10-15'),
                                           ('eda2d833-f6b5-4209-ba31-b7782a104f22', 'region 10', 'contact type 1', 'No', 'GSA', 'Yes', 'No', 'No', 63, 4547, 3263, 'No', 'No', 'city 10', 'rejected', 36578, NULL, 'No', '2023-10-15'),
                                           ('2461191a-ca2d-4840-aeb9-0b7eb87cfd31', 'region 11', 'contact type 2', 'No', 'MAC', 'Yes', 'Yes', 'Yes', 34, 2553, 4356, 'No', 'Yes', 'city 11', 'rejected', 48578, 'Won', 'Yes', '2023-10-15'),
                                           ('21ab4dec-6c0e-44c8-ad87-beb989f53d3b', 'region 12', 'contact type 3', 'No', 'GSA', 'No', 'Yes', 'Yes', 4525, 637, 6456, 'No', 'Yes', 'city 12', 'rejected', 11689, 'Won', 'Yes', '2023-10-15'),
                                           ('e4af8e90-cf27-4b3d-b979-b33e71005bce', 'region 13', 'contact type 1', 'No', 'GSA', 'Yes', 'No', 'Yes', 34, 858, 746, 'Yes', 'No', 'city 13', 'approved', 13589, 'Won', 'Yes', '2023-10-15'),
                                           ('9c21ac60-b016-4c8a-9c78-3febae04e70e', 'region 14', 'contact type 2', 'Yes', 'GSA', 'Yes', 'No', 'No', 734, 346, 346, 'Yes', 'Yes', 'city 14', 'approved', 544, 'Won', 'Yes', '2023-10-15'),
                                           ('6bfb38ae-7229-4efe-8189-5efa634ad9ca', 'region 15', 'contact type 3', 'No', 'MAC', 'Yes', 'No', 'No', 524, 2111, 747, 'No', 'No', 'city 15', 'rejected', 7898, 'Won', 'Yes', '2023-10-15'),
                                           ('15ee389b-0539-4a8c-b773-7515adc8c223', 'region 16', 'contact type 1', 'No', 'GSA', 'Yes', 'No', 'No', 136, 425, 2453, 'No', 'Yes', 'city 16', 'rejected', 6583, 'Lost', 'Yes', '2023-10-15'),
                                           ('491daf31-1a1b-44dd-8871-e39a15cc44c1', 'region 17', 'contact type 2', 'No', 'MAC', 'Yes', 'Yes', 'No', 423, 678, 743, 'No', 'Yes', 'city 17', 'approved', 19875, 'Lost', 'Yes', '2023-10-15'),
                                           ('bc33d4f5-7091-4fa2-822d-8ba85d99129e', 'region 18', 'contact type 3', 'No', 'GSA', 'No', 'No', 'No', 1134, 346, 3435, 'No', 'Yes', 'city 18', 'approved', 17987, 'Lost', 'Yes', '2023-10-15'),
                                           ('a423a42b-81ee-481f-ba96-d715703d19b3', 'region 19', 'contact type 1', 'No', 'GSA', 'Yes', 'No', 'No', 823, 5735, 2535, 'No', 'Yes', 'city 19', 'approved', 24898, 'Lost', 'Yes', '2023-10-15'),
                                           ('b0300d15-50eb-45b5-a520-cbca07345ec7', 'region 20', 'contact type 2', 'No', 'MAC', 'Yes', 'No', 'Yes', 346, 4656, 6343, 'No', 'Yes', 'city 20', 'approved', 5889, 'Lost', 'Yes', '2023-10-15'),
                                           ('30bbd0f6-6b8f-4364-9b87-5e8bac84c504', 'region 21', 'contact type 3', 'Yes', 'GSA', 'Yes', 'Yes', 'Yes', 633, 2434, 632, 'No', 'Yes', 'city 21', 'approved', 29878, NULL, 'No', '2023-10-15'),
                                           ('95e0f670-bc4a-44c2-9605-0e6e44062770', 'region 22', 'contact type 1', 'No', 'GSA', 'Yes', 'No', 'Yes', 1734, 453, 924, 'No', 'Yes', 'city 22', 'rejected', 8965, 'Won', 'Yes', '2023-10-15'),
                                           ('fd8a6c9c-82aa-4570-8c8a-731a2687ff69', 'region 23', 'contact type 2', 'No', 'MAC', 'Yes', 'No', 'Yes', 1254, 7454, 644, 'No', 'Yes', 'city 23', 'approved', 13568, NULL, 'No', '2023-10-15'),
                                           ('32dcd94b-3de8-4782-8ead-cfa8a8189fa7', 'region 24', 'contact type 3', 'No', 'GSA', 'No', 'No', 'Yes', 2345, 23, 6435, 'Yes', 'No', 'city 24', 'approved', 18769, 'Won', 'Yes', '2023-10-15'),
                                           ('8377429a-1f84-4bfa-b94d-a73d5cf3be0e', 'region 25', 'contact type 1', 'No', 'GSA', 'Yes', 'No', 'No', 1232, 646, 647, 'No', 'No', 'city 25', 'rejected', 24589, NULL, 'No', '2023-10-15'),
                                           ('e4dd4cc5-77f7-4c63-afe9-7917ee0435e5', 'region 26', 'contact type 2', 'Yes', 'GSA', 'Yes', 'No', 'No', 745, 864, 4745, 'No', 'Yes', 'city 26', 'approved', 14689, NULL, 'No', '2023-10-15'),
                                           ('3e4082ab-9d92-4803-b13d-8c9d6cd6ebb6', 'region 27', 'contact type 3', 'No', 'GSA', 'Yes', 'Yes', 'No', 346, 774, 452, 'No', 'Yes', 'city 27', 'rejected', 50556, 'Lost', 'Yes', '2023-10-15'),
                                           ('47f9f723-54a9-431f-8509-3607f5c4801a', 'region 28', 'contact type 1', 'No', 'MAC', 'Yes', 'No', 'No', 945, 1111, 3132, 'Yes', 'No', 'city 28', 'approved', 4158, NULL, 'No', '2023-10-15'),
                                           ('fa164068-083a-4558-823d-ff6839b8cf8c', 'region 29', 'contact type 2', 'No', 'MAC', 'Yes', 'Yes', 'No', 735, 4243, 2356, 'No', 'No', 'city 29', 'approved', 29874, NULL, 'No', '2023-10-15'),
                                           ('a74c8a43-9eec-4693-802b-ff5550a9aa4d', 'region 30', 'contact type 3', 'No', 'MAC', 'Yes', 'Yes', 'No', 345, 3234, 5342, 'Yes', 'Yes', 'city 30', 'rejected', 12001, 'Won', 'Yes', '2023-10-15');

  INSERT INTO case_data VALUES ('6b9f211e-c70c-42d2-b663-a236313526cd', 'job 1', 0, '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-07-03 02:01:22.708337+00', 'description job 1', 'location 1', 'open', '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-07-03 02:01:22.708337+00'),
                               ('9d51bd7f-d523-49dc-a974-b377b8a21c26', 'job 2', 0, 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-07-06 02:01:22.708337+00', 'description job 2', 'location 2', 'open', 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-07-06 02:01:22.708337+00'),
                               ('6d8600aa-91f9-47fc-a52c-9e1ef503c418', 'job 3', 0, 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-07-08 02:01:22.708337+00', 'description job 3', 'location 3', 'open', 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-07-08 02:01:22.708337+00'),
                               ('b9f0cd00-c8b4-4ad3-8119-79bb934592ee', 'job 4', 0, 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-07-14 02:01:22.708337+00', 'description job 4', 'location 4', 'cold', 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-07-15 02:01:22.708337+00'),
                               ('c805fe68-a656-4b74-9a35-2779148863c8', 'job 5', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-07-19 02:01:22.708337+00', 'description job 5', 'location 5', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-07-19 02:01:22.708337+00'),
                               ('926bdf7e-c6e4-460b-b33a-28c5bb890a44', 'job 6', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-07-25 02:01:22.708337+00', 'description job 6', 'location 6', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-07-25 02:01:22.708337+00'),
                               ('afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f', 'job 7', 0, '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-07-28 02:01:22.708337+00', 'description job 7', 'location 7', 'open', '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-07-28 02:01:22.708337+00'),
                               ('2f3e2095-4aff-440f-bb75-a98097322d88', 'job 8', 0, '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-08-02 02:01:22.708337+00', 'description job 8', 'location 8', 'archive', '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-08-04 02:01:22.708337+00'),
                               ('30bc8170-1377-419d-ae2a-89057d91e842', 'job 9', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-08-07 02:01:22.708337+00', 'description job 9', 'location 9', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-08-07 02:01:22.708337+00'),
                               ('eda2d833-f6b5-4209-ba31-b7782a104f22', 'job 10', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-08-09 02:01:22.708337+00', 'description job 10', 'location 10', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-08-09 02:01:22.708337+00'),
                               ('2461191a-ca2d-4840-aeb9-0b7eb87cfd31', 'job 11', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-08-15 02:01:22.708337+00', 'description job 11', 'location 11', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-08-15 02:01:22.708337+00'),
                               ('21ab4dec-6c0e-44c8-ad87-beb989f53d3b', 'job 12', 0, 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-08-16 02:01:22.708337+00', 'description job 12', 'location 12', 'open', 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-08-16 02:01:22.708337+00'),
                               ('e4af8e90-cf27-4b3d-b979-b33e71005bce', 'job 13', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-08-20 02:01:22.708337+00', 'description job 13', 'location 13', 'archive', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-08-22 02:01:22.708337+00'),
                               ('9c21ac60-b016-4c8a-9c78-3febae04e70e', 'job 14', 0, '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-08-22 02:01:22.708337+00', 'description job 14', 'location 14', 'open', '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-08-22 02:01:22.708337+00'),
                               ('6bfb38ae-7229-4efe-8189-5efa634ad9ca', 'job 15', 0, '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-08-26 02:01:22.708337+00', 'description job 15', 'location 15', 'close', '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-08-27 02:01:22.708337+00'),
                               ('15ee389b-0539-4a8c-b773-7515adc8c223', 'job 16', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-03 02:01:22.708337+00', 'description job 16', 'location 16', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-03 02:01:22.708337+00'),
                               ('491daf31-1a1b-44dd-8871-e39a15cc44c1', 'job 17', 0, 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-05 02:01:22.708337+00', 'description job 17', 'location 17', 'open', 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-05 02:01:22.708337+00'),
                               ('bc33d4f5-7091-4fa2-822d-8ba85d99129e', 'job 18', 0, '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-09-08 02:01:22.708337+00', 'description job 18', 'location 18', 'open', '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-09-08 02:01:22.708337+00'),
                               ('a423a42b-81ee-481f-ba96-d715703d19b3', 'job 19', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-11 02:01:22.708337+00', 'description job 19', 'location 19', 'cold', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-12 02:01:22.708337+00'),
                               ('b0300d15-50eb-45b5-a520-cbca07345ec7', 'job 20', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-12 02:01:22.708337+00', 'description job 20', 'location 20', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-12 02:01:22.708337+00'),
                               ('30bbd0f6-6b8f-4364-9b87-5e8bac84c504', 'job 21', 0, 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-13 02:01:22.708337+00', 'description job 21', 'location 21', 'open', 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-13 02:01:22.708337+00'),
                               ('95e0f670-bc4a-44c2-9605-0e6e44062770', 'job 22', 0, 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-17 02:01:22.708337+00', 'description job 22', 'location 22', 'open', 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-17 02:01:22.708337+00'),
                               ('fd8a6c9c-82aa-4570-8c8a-731a2687ff69', 'job 23', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-19 02:01:22.708337+00', 'description job 23', 'location 23', 'close', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-20 02:01:22.708337+00'),
                               ('32dcd94b-3de8-4782-8ead-cfa8a8189fa7', 'job 24', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-22 02:01:22.708337+00', 'description job 24', 'location 24', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-22 02:01:22.708337+00'),
                               ('8377429a-1f84-4bfa-b94d-a73d5cf3be0e', 'job 25', 0, 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-24 02:01:22.708337+00', 'description job 25', 'location 25', 'archive', 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-26 02:01:22.708337+00'),
                               ('e4dd4cc5-77f7-4c63-afe9-7917ee0435e5', 'job 26', 0, '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-09-25 02:01:22.708337+00', 'description job 26', 'location 26', 'open', '8d55ac74-d71f-47ec-8e80-17ead7709a5c', '2023-09-25 02:01:22.708337+00'),
                               ('3e4082ab-9d92-4803-b13d-8c9d6cd6ebb6', 'job 27', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-27 02:01:22.708337+00', 'description job 27', 'location 27', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-09-27 02:01:22.708337+00'),
                               ('47f9f723-54a9-431f-8509-3607f5c4801a', 'job 28', 0, 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-29 02:01:22.708337+00', 'description job 28', 'location 28', 'close', 'e8201944-dfd5-4dc2-850f-5827959ada60', '2023-09-30 02:01:22.708337+00'),
                               ('fa164068-083a-4558-823d-ff6839b8cf8c', 'job 29', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-10-01 02:01:22.708337+00', 'description job 29', 'location 29', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-10-01 02:01:22.708337+00'),
                               ('a74c8a43-9eec-4693-802b-ff5550a9aa4d', 'job 30', 0, '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-10-02 02:01:22.708337+00', 'description job 30', 'location 30', 'open', '12a755f6-cd57-4cc9-8760-fe8845221244', '2023-10-02 02:01:22.708337+00');

  INSERT INTO case_status_history (case_id, status, status_update_date) VALUES ('6b9f211e-c70c-42d2-b663-a236313526cd', 'open', '2023-07-03 02:01:22.708337+00'),
                                         ('9d51bd7f-d523-49dc-a974-b377b8a21c26', 'open', '2023-07-06 02:01:22.708337+00'),
                                         ('6d8600aa-91f9-47fc-a52c-9e1ef503c418', 'open', '2023-07-08 02:01:22.708337+00'),
                                         ('b9f0cd00-c8b4-4ad3-8119-79bb934592ee', 'open', '2023-07-14 02:01:22.708337+00'),
                                         ('b9f0cd00-c8b4-4ad3-8119-79bb934592ee', 'cold', '2023-07-15 02:01:22.708337+00'),
                                         ('c805fe68-a656-4b74-9a35-2779148863c8', 'open', '2023-07-19 02:01:22.708337+00'),
                                         ('926bdf7e-c6e4-460b-b33a-28c5bb890a44', 'open', '2023-07-25 02:01:22.708337+00'),
                                         ('afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f', 'open', '2023-07-28 02:01:22.708337+00'),
                                         ('2f3e2095-4aff-440f-bb75-a98097322d88', 'open', '2023-08-02 02:01:22.708337+00'),
                                         ('2f3e2095-4aff-440f-bb75-a98097322d88', 'close', '2023-08-03 02:01:22.708337+00'),
                                         ('2f3e2095-4aff-440f-bb75-a98097322d88', 'archive', '2023-08-04 02:01:22.708337+00'),
                                         ('30bc8170-1377-419d-ae2a-89057d91e842', 'open', '2023-08-07 02:01:22.708337+00'),
                                         ('eda2d833-f6b5-4209-ba31-b7782a104f22', 'open', '2023-08-09 02:01:22.708337+00'),
                                         ('2461191a-ca2d-4840-aeb9-0b7eb87cfd31', 'open', '2023-08-15 02:01:22.708337+00'),
                                         ('21ab4dec-6c0e-44c8-ad87-beb989f53d3b', 'open', '2023-08-16 02:01:22.708337+00'),
                                         ('e4af8e90-cf27-4b3d-b979-b33e71005bce', 'open', '2023-08-20 02:01:22.708337+00'),
                                         ('e4af8e90-cf27-4b3d-b979-b33e71005bce', 'close', '2023-08-21 02:01:22.708337+00'),
                                         ('e4af8e90-cf27-4b3d-b979-b33e71005bce', 'archive', '2023-08-22 02:01:22.708337+00'),
                                         ('9c21ac60-b016-4c8a-9c78-3febae04e70e', 'open', '2023-08-22 02:01:22.708337+00'),
                                         ('6bfb38ae-7229-4efe-8189-5efa634ad9ca', 'open', '2023-08-26 02:01:22.708337+00'),
                                         ('6bfb38ae-7229-4efe-8189-5efa634ad9ca', 'close', '2023-08-27 02:01:22.708337+00'),
                                         ('15ee389b-0539-4a8c-b773-7515adc8c223', 'open', '2023-09-03 02:01:22.708337+00'),
                                         ('491daf31-1a1b-44dd-8871-e39a15cc44c1', 'open', '2023-09-05 02:01:22.708337+00'),
                                         ('bc33d4f5-7091-4fa2-822d-8ba85d99129e', 'open', '2023-09-08 02:01:22.708337+00'),
                                         ('a423a42b-81ee-481f-ba96-d715703d19b3', 'open', '2023-09-11 02:01:22.708337+00'),
                                         ('a423a42b-81ee-481f-ba96-d715703d19b3', 'cold', '2023-09-12 02:01:22.708337+00'),
                                         ('b0300d15-50eb-45b5-a520-cbca07345ec7', 'open', '2023-09-12 02:01:22.708337+00'),
                                         ('30bbd0f6-6b8f-4364-9b87-5e8bac84c504', 'open', '2023-09-13 02:01:22.708337+00'),
                                         ('95e0f670-bc4a-44c2-9605-0e6e44062770', 'open', '2023-09-17 02:01:22.708337+00'),
                                         ('fd8a6c9c-82aa-4570-8c8a-731a2687ff69', 'open', '2023-09-19 02:01:22.708337+00'),
                                         ('fd8a6c9c-82aa-4570-8c8a-731a2687ff69', 'close', '2023-09-20 02:01:22.708337+00'),
                                         ('32dcd94b-3de8-4782-8ead-cfa8a8189fa7', 'open', '2023-09-22 02:01:22.708337+00'),
                                         ('8377429a-1f84-4bfa-b94d-a73d5cf3be0e', 'open', '2023-09-24 02:01:22.708337+00'),
                                         ('8377429a-1f84-4bfa-b94d-a73d5cf3be0e', 'close', '2023-09-25 02:01:22.708337+00'),
                                         ('8377429a-1f84-4bfa-b94d-a73d5cf3be0e', 'archive', '2023-09-26 02:01:22.708337+00'),
                                         ('e4dd4cc5-77f7-4c63-afe9-7917ee0435e5', 'open', '2023-09-25 02:01:22.708337+00'),
                                         ('3e4082ab-9d92-4803-b13d-8c9d6cd6ebb6', 'open', '2023-09-27 02:01:22.708337+00'),
                                         ('47f9f723-54a9-431f-8509-3607f5c4801a', 'open', '2023-09-29 02:01:22.708337+00'),
                                         ('47f9f723-54a9-431f-8509-3607f5c4801a', 'close', '2023-09-30 02:01:22.708337+00'),
                                         ('fa164068-083a-4558-823d-ff6839b8cf8c', 'open', '2023-10-01 02:01:22.708337+00'),
                                         ('a74c8a43-9eec-4693-802b-ff5550a9aa4d', 'open', '2023-10-02 02:01:22.708337+00');

  INSERT INTO workflow_task VALUES ('0276ab82-db40-4a5e-9fcc-1a5b348c6df2','cba0ddeb-4372-47f8-a75d-5c1bb0605e04','{"bond": 3263, "city": "city 10", "omnia": "Yes", "caseId": "eda2d833-f6b5-4209-ba31-b7782a104f22", "credit": 4547, "region": "region 10", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 36578, "prevailingWage": 63, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','01/07/2023  07:18:05','01/07/2023  07:18:05','01/07/2023  07:18:05','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('04ad7555-3981-40f4-a6fd-dd9235bb1f8b','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 3132.0, "city": "city 28", "omnia": "Yes", "caseId": "47f9f723-54a9-431f-8509-3607f5c4801a", "credit": 1111.0, "region": "region 28", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 4158.0, "prevailingWage": 945.0, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','02/07/2023  07:26:32','02/07/2023  07:26:32','02/07/2023  07:26:32','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('06581ede-6b09-4d1b-ad8f-adda3a2955c7','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 744.0, "city": "city 1", "omnia": "Yes", "caseId": "6b9f211e-c70c-42d2-b663-a236313526cd", "credit": 643.0, "region": "region 1", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 15000.0, "prevailingWage": 234.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','03/07/2023  07:11:32','03/07/2023  07:11:32','03/07/2023  07:11:32','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('0b35a808-1947-4370-8ef1-def558ecc142','66208b54-fd31-4c84-ac95-7d16e04057ce','{"bond": 3263, "city": "city 10", "omnia": "Yes", "caseId": "eda2d833-f6b5-4209-ba31-b7782a104f22", "credit": 4547, "region": "region 10", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 36578, "prevailingWage": 63, "badgingRequired": "No", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','04/07/2023  07:18:09','04/07/2023  07:18:09','04/07/2023  07:18:09','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('0bb1c756-be89-4dbc-b118-66b3a695ee4c','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 2434.0, "city": "city 7", "omnia": "Yes", "caseId": "afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f", "credit": 3634.0, "region": "region 7", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 29365.0, "prevailingWage": 68.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','05/07/2023  07:15:19','05/07/2023  07:15:19','05/07/2023  07:15:19','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('0e0b2f2b-bddb-4bcb-9029-fe7bd9a2613a','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','06/07/2023  07:19:45','06/07/2023  07:19:45','06/07/2023  07:19:45','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('0e811513-2c75-45f2-ac66-6ca61ad2ac31','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 747.0, "city": "city 15", "omnia": "Yes", "caseId": "6bfb38ae-7229-4efe-8189-5efa634ad9ca", "credit": 2111.0, "region": "region 15", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 7898.0, "prevailingWage": 524.0, "badgingRequired": "No", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',FALSE,FALSE,FALSE,NULL,NULL,NULL,'07/07/2023  07:33:50',NULL,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('10053e2b-f02f-48c6-9a95-1b0ddd0a6782','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 2434, "city": "city 7", "omnia": "Yes", "caseId": "afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f", "credit": 3634, "region": "region 7", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 29365, "prevailingWage": 68, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','07/07/2023  07:15:24','07/07/2023  07:15:24','07/07/2023  07:15:24','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('131a075d-d145-4d03-b406-e823a74b58cf','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 7456, "city": "city 4", "omnia": "Yes", "caseId": "b9f0cd00-c8b4-4ad3-8119-79bb934592ee", "credit": 3635, "region": "region 4", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 9885, "prevailingWage": 784, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','08/07/2023  07:13:01','08/07/2023  07:13:01','08/07/2023  07:13:01','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('1630f76f-a2cb-4089-a94a-a9eae89b1c9a','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 3132, "city": "city 28", "omnia": "Yes", "caseId": "47f9f723-54a9-431f-8509-3607f5c4801a", "credit": 1111, "region": "region 28", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 4158, "prevailingWage": 945, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','09/07/2023  07:26:35','09/07/2023  07:26:35','09/07/2023  07:26:35','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('1db21d6e-8c30-47b9-8ebd-c6741f43b500','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','10/07/2023  07:20:15','10/07/2023  07:20:15','10/07/2023  07:20:15','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('201420a6-bf35-4012-b9f6-75055e9dafdf','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 2535, "city": "city 19", "omnia": "Yes", "caseId": "a423a42b-81ee-481f-ba96-d715703d19b3", "credit": 5735, "region": "region 19", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 24898, "prevailingWage": 823, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','11/07/2023  07:21:40','11/07/2023  07:21:40','11/07/2023  07:21:40','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('214e6274-6521-4288-b3eb-8d7f8ae871c3','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 6556, "city": "city 8", "omnia": "No", "caseId": "2f3e2095-4aff-440f-bb75-a98097322d88", "credit": 454, "region": "region 8", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 2658, "prevailingWage": 364, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','12/07/2023  07:16:48','12/07/2023  07:16:48','12/07/2023  07:16:48','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('24b27200-c6db-4d08-bc87-445fec30fe51','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 632, "city": "city 21", "omnia": "Yes", "caseId": "30bbd0f6-6b8f-4364-9b87-5e8bac84c504", "credit": 2434, "region": "region 21", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 29878, "prevailingWage": 633, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','13/07/2023  07:22:42','13/07/2023  07:22:42','13/07/2023  07:22:42','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('267289b5-a41e-46c5-830e-c7b6939684b7','66208b54-fd31-4c84-ac95-7d16e04057ce','{"bond": 4356, "city": "city 11", "omnia": "Yes", "caseId": "2461191a-ca2d-4840-aeb9-0b7eb87cfd31", "credit": 2553, "region": "region 11", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 48578, "prevailingWage": 34, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','14/07/2023  07:18:44','14/07/2023  07:18:44','14/07/2023  07:18:44','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('27c9a200-c779-499f-8eb9-07e314f5cfff','66208b54-fd31-4c84-ac95-7d16e04057ce','{"bond": 452, "city": "city 27", "omnia": "Yes", "caseId": "3e4082ab-9d92-4803-b13d-8c9d6cd6ebb6", "credit": 774, "region": "region 27", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 50556, "prevailingWage": 346, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','15/07/2023  07:26:00','15/07/2023  07:26:00','15/07/2023  07:26:00','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('2a598ca6-fb2b-4fa2-8551-4e1b4c80f7ec','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 644, "city": "city 23", "omnia": "Yes", "caseId": "fd8a6c9c-82aa-4570-8c8a-731a2687ff69", "credit": 7454, "region": "region 23", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 13568, "prevailingWage": 1254, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','16/07/2023  07:23:54','16/07/2023  07:23:54','16/07/2023  07:23:54','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('2b7b62df-d67e-4422-aec5-c5bcf10a6252','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 632, "city": "city 21", "omnia": "Yes", "caseId": "30bbd0f6-6b8f-4364-9b87-5e8bac84c504", "credit": 2434, "region": "region 21", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 29878, "prevailingWage": 633, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','17/07/2023  07:22:44','17/07/2023  07:22:44','17/07/2023  07:22:44','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('2db82dc2-942f-4457-b049-221f6ae20971','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','18/07/2023  07:20:18','18/07/2023  07:20:18','18/07/2023  07:20:18','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('2e52cf13-c494-417a-b8d9-66fe70845dc9','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 2356, "city": "city 29", "omnia": "Yes", "caseId": "fa164068-083a-4558-823d-ff6839b8cf8c", "credit": 4243, "region": "region 29", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 29874, "prevailingWage": 735, "badgingRequired": "No", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','19/07/2023  07:31:51','19/07/2023  07:31:51','19/07/2023  07:31:51','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('2fe25072-2c11-437a-928e-543fb7c4934b','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 4745.0, "city": "city 26", "omnia": "Yes", "caseId": "e4dd4cc5-77f7-4c63-afe9-7917ee0435e5", "credit": 864.0, "region": "region 26", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 14689.0, "prevailingWage": 745.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','20/07/2023  07:24:59','20/07/2023  07:24:59','20/07/2023  07:24:59','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('32e444a7-6352-422e-bfaa-425a87e8e69c','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 6556, "city": "city 8", "omnia": "No", "caseId": "2f3e2095-4aff-440f-bb75-a98097322d88", "credit": 454, "region": "region 8", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 2658, "prevailingWage": 364, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','21/07/2023  07:16:55','21/07/2023  07:16:55','21/07/2023  07:16:55','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('338a4634-d6c4-47f0-b25e-5975b149c868','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 996, "city": "city 5", "omnia": "Yes", "caseId": "c805fe68-a656-4b74-9a35-2779148863c8", "credit": 2314, "region": "region 5", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 23665, "prevailingWage": 433, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','22/07/2023  07:13:48','22/07/2023  07:13:48','22/07/2023  07:13:48','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('3aed5463-2750-4b7e-9a2e-43d194b6de32','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 996.0, "city": "city 5", "omnia": "Yes", "caseId": "c805fe68-a656-4b74-9a35-2779148863c8", "credit": 2314.0, "region": "region 5", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 23665.0, "prevailingWage": 433.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','23/07/2023  07:13:37','23/07/2023  07:13:37','23/07/2023  07:13:37','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('3cd3a78f-6745-4ffd-86a3-81c536085f35','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 74, "city": "city 2", "omnia": "No", "caseId": "9d51bd7f-d523-49dc-a974-b377b8a21c26", "credit": 5345, "region": "region 2", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 21000, "prevailingWage": 6345, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','24/07/2023  07:12:09','24/07/2023  07:12:09','24/07/2023  07:12:09','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('3d24164e-ffbd-4dd2-b705-f70dd525b584','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 2535.0, "city": "city 19", "omnia": "Yes", "caseId": "a423a42b-81ee-481f-ba96-d715703d19b3", "credit": 5735.0, "region": "region 19", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 24898.0, "prevailingWage": 823.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','25/07/2023  07:21:26','25/07/2023  07:21:26','25/07/2023  07:21:26','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('3d496b68-1ea2-401d-8eac-f9c5e0007b60','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 74, "city": "city 2", "omnia": "No", "caseId": "9d51bd7f-d523-49dc-a974-b377b8a21c26", "credit": 5345, "region": "region 2", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 21000, "prevailingWage": 6345, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','26/07/2023  07:12:11','26/07/2023  07:12:11','26/07/2023  07:12:11','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('3dfe3f27-e588-40e0-a9ca-f97ef517ed9d','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 74.0, "city": "city 2", "omnia": "No", "caseId": "9d51bd7f-d523-49dc-a974-b377b8a21c26", "credit": 5345.0, "region": "region 2", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 21000.0, "prevailingWage": 6345.0, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','27/07/2023  07:11:58','27/07/2023  07:11:58','27/07/2023  07:11:58','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('3f3d7679-f305-4c6e-af1c-d42fdf36c719','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 924, "city": "city 22", "omnia": "Yes", "caseId": "95e0f670-bc4a-44c2-9605-0e6e44062770", "credit": 453, "region": "region 22", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 8965, "prevailingWage": 1734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','28/07/2023  07:23:19','28/07/2023  07:23:19','28/07/2023  07:23:19','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('40637af0-c6af-4fa7-8555-2a0379620c02','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 6343, "city": "city 20", "omnia": "Yes", "caseId": "b0300d15-50eb-45b5-a520-cbca07345ec7", "credit": 4656, "region": "region 20", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 5889, "prevailingWage": 346, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','29/07/2023  07:22:02','29/07/2023  07:22:02','29/07/2023  07:22:02','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('407ef8f7-bca8-432d-84e4-0b044a3ae424','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 452, "city": "city 27", "omnia": "Yes", "caseId": "3e4082ab-9d92-4803-b13d-8c9d6cd6ebb6", "credit": 774, "region": "region 27", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 50556, "prevailingWage": 346, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','30/07/2023  07:25:48','30/07/2023  07:25:48','30/07/2023  07:25:48','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('40fe5829-566d-4c5d-8091-3b16bce38cfd','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 644, "city": "city 23", "omnia": "Yes", "caseId": "fd8a6c9c-82aa-4570-8c8a-731a2687ff69", "credit": 7454, "region": "region 23", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 13568, "prevailingWage": 1254, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','31/07/2023  07:23:51','31/07/2023  07:23:51','31/07/2023  07:23:51','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('42c91d02-c35a-4ee9-8dc2-7f86a5d96181','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 6556, "city": "city 8", "omnia": "No", "caseId": "2f3e2095-4aff-440f-bb75-a98097322d88", "credit": 454, "region": "region 8", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 2658, "prevailingWage": 364, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','01/07/2023  07:17:01','01/07/2023  07:17:01','01/07/2023  07:17:01','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('46b2f00e-78b5-45b2-849d-b48656fe0787','cba0ddeb-4372-47f8-a75d-5c1bb0605e04','{"bond": 2356, "city": "city 29", "omnia": "Yes", "caseId": "fa164068-083a-4558-823d-ff6839b8cf8c", "credit": 4243, "region": "region 29", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 29874, "prevailingWage": 735, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','02/07/2023  07:31:57','02/07/2023  07:31:57','02/07/2023  07:31:57','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('479c497b-3ccf-4d39-ae4e-d1d7c3b48686','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 3132, "city": "city 28", "omnia": "Yes", "caseId": "47f9f723-54a9-431f-8509-3607f5c4801a", "credit": 1111, "region": "region 28", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 4158, "prevailingWage": 945, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','03/07/2023  07:26:44','03/07/2023  07:26:44','03/07/2023  07:26:44','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('48079936-5f8b-49be-b522-467990b3d129','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 4745, "city": "city 26", "omnia": "Yes", "caseId": "e4dd4cc5-77f7-4c63-afe9-7917ee0435e5", "credit": 864, "region": "region 26", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 14689, "prevailingWage": 745, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','04/07/2023  07:25:04','04/07/2023  07:25:04','04/07/2023  07:25:04','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('49b8aacf-cf46-4426-8785-8853363d144b','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 2535, "city": "city 19", "omnia": "Yes", "caseId": "a423a42b-81ee-481f-ba96-d715703d19b3", "credit": 5735, "region": "region 19", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 24898, "prevailingWage": 823, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','05/07/2023  07:21:32','05/07/2023  07:21:32','05/07/2023  07:21:32','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('4c7476c9-1432-45f6-bb96-239c5d10359c','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 2356.0, "city": "city 29", "omnia": "Yes", "caseId": "fa164068-083a-4558-823d-ff6839b8cf8c", "credit": 4243.0, "region": "region 29", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 29874.0, "prevailingWage": 735.0, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','06/07/2023  07:31:43','06/07/2023  07:31:43','06/07/2023  07:31:43','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('4fe3f0f0-cd5a-44bb-925c-1ffc9f41e2a0','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 3435.0, "city": "city 18", "omnia": "No", "caseId": "bc33d4f5-7091-4fa2-822d-8ba85d99129e", "credit": 346.0, "region": "region 18", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 17987.0, "prevailingWage": 1134.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','07/07/2023  07:35:01','07/07/2023  07:35:01','07/07/2023  07:35:01','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('50bda65e-16ff-4bc0-90ec-36f28e8b15c2','cba0ddeb-4372-47f8-a75d-5c1bb0605e04','{"bond": 4356, "city": "city 11", "omnia": "Yes", "caseId": "2461191a-ca2d-4840-aeb9-0b7eb87cfd31", "credit": 2553, "region": "region 11", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 48578, "prevailingWage": 34, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','08/07/2023  07:18:41','08/07/2023  07:18:41','08/07/2023  07:18:41','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('5217bac8-7971-4109-9a31-d6e137229ab5','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 632.0, "city": "city 21", "omnia": "Yes", "caseId": "30bbd0f6-6b8f-4364-9b87-5e8bac84c504", "credit": 2434.0, "region": "region 21", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 29878.0, "prevailingWage": 633.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','09/08/2023  07:22:29','09/08/2023  07:22:29','09/08/2023  07:22:29','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('534d54f1-d355-409d-98d5-e8aa737123bb','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 743, "city": "city 17", "omnia": "Yes", "caseId": "491daf31-1a1b-44dd-8871-e39a15cc44c1", "credit": 678, "region": "region 17", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 19875, "prevailingWage": 423, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','10/08/2023  07:21:04','10/08/2023  07:21:04','10/08/2023  07:21:04','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('534df9ef-f875-45bd-bd48-eb4ce7514031','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 2453.0, "city": "city 16", "omnia": "Yes", "caseId": "15ee389b-0539-4a8c-b773-7515adc8c223", "credit": 425.0, "region": "region 16", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 6583.0, "prevailingWage": 136.0, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','11/08/2023  07:34:20','11/08/2023  07:34:20','11/08/2023  07:34:20','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('5451f378-23ec-44df-8c98-334b128683f8','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 743, "city": "city 17", "omnia": "Yes", "caseId": "491daf31-1a1b-44dd-8871-e39a15cc44c1", "credit": 678, "region": "region 17", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 19875, "prevailingWage": 423, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','12/08/2023  07:20:56','12/08/2023  07:20:56','12/08/2023  07:20:56','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('54e3b2cb-f19c-4e5b-bd81-f44318ed8e79','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 6435, "city": "city 24", "omnia": "No", "caseId": "32dcd94b-3de8-4782-8ead-cfa8a8189fa7", "credit": 23, "region": "region 24", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 18769, "prevailingWage": 2345, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','13/08/2023  07:24:41','13/08/2023  07:24:41','13/08/2023  07:24:41','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('568c8706-4fa3-4caa-8847-425a5180dcaf','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 2535, "city": "city 19", "omnia": "Yes", "caseId": "a423a42b-81ee-481f-ba96-d715703d19b3", "credit": 5735, "region": "region 19", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 24898, "prevailingWage": 823, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','14/08/2023  07:21:34','14/08/2023  07:21:34','14/08/2023  07:21:34','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('57e53df5-2d2f-494e-884b-5aa5a5cb3399','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 6456, "city": "city 12", "omnia": "No", "caseId": "21ab4dec-6c0e-44c8-ad87-beb989f53d3b", "credit": 637, "region": "region 12", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 11689, "prevailingWage": 4525, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','15/08/2023  07:19:09','15/08/2023  07:19:09','15/08/2023  07:19:09','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('5d6c027e-b9bb-40e9-b97e-b2d74dca20a0','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 3263, "city": "city 10", "omnia": "Yes", "caseId": "eda2d833-f6b5-4209-ba31-b7782a104f22", "credit": 4547, "region": "region 10", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 36578, "prevailingWage": 63, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','16/08/2023  07:18:00','16/08/2023  07:18:00','16/08/2023  07:18:00','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('5d79c749-806b-49a5-a98e-c24bc9e73978','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','17/08/2023  07:19:53','17/08/2023  07:19:53','17/08/2023  07:19:53','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('5dda415e-db3f-4ab0-9663-9685fd64a234','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 743.0, "city": "city 17", "omnia": "Yes", "caseId": "491daf31-1a1b-44dd-8871-e39a15cc44c1", "credit": 678.0, "region": "region 17", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 19875.0, "prevailingWage": 423.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','18/08/2023  07:20:53','18/08/2023  07:20:53','18/08/2023  07:20:53','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6173ca33-b356-4412-82bc-88a75efd1ce7','66208b54-fd31-4c84-ac95-7d16e04057ce','{"bond": 2356, "city": "city 29", "omnia": "Yes", "caseId": "fa164068-083a-4558-823d-ff6839b8cf8c", "credit": 4243, "region": "region 29", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 29874, "prevailingWage": 735, "badgingRequired": "No", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','19/08/2023  07:31:59','19/08/2023  07:31:59','19/08/2023  07:31:59','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('623379ea-f9d2-4ff3-b5c3-6055302bb8ad','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 6343.0, "city": "city 20", "omnia": "Yes", "caseId": "b0300d15-50eb-45b5-a520-cbca07345ec7", "credit": 4656.0, "region": "region 20", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 5889.0, "prevailingWage": 346.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','20/08/2023  07:21:53','20/08/2023  07:21:53','20/08/2023  07:21:53','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('65c2b70c-6676-4793-b9ee-eba3968972f5','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 7456.0, "city": "city 4", "omnia": "Yes", "caseId": "b9f0cd00-c8b4-4ad3-8119-79bb934592ee", "credit": 3635.0, "region": "region 4", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 9885.0, "prevailingWage": 784.0, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','21/08/2023  07:12:57','21/08/2023  07:12:57','21/08/2023  07:12:57','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('66c35c55-2d53-47ec-a06f-d9a56c4cb990','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 644, "city": "city 23", "omnia": "Yes", "caseId": "fd8a6c9c-82aa-4570-8c8a-731a2687ff69", "credit": 7454, "region": "region 23", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 13568, "prevailingWage": 1254, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','22/08/2023  07:23:57','22/08/2023  07:23:57','22/08/2023  07:23:57','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('67d09400-cd88-4047-b331-ead0d988e2ca','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 6435.0, "city": "city 24", "omnia": "No", "caseId": "32dcd94b-3de8-4782-8ead-cfa8a8189fa7", "credit": 23.0, "region": "region 24", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 18769.0, "prevailingWage": 2345.0, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','23/08/2023  07:24:31','23/08/2023  07:24:31','23/08/2023  07:24:31','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('68d099f2-1cd0-47ab-8904-28f9ba9b62e0','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','24/08/2023  07:19:47','24/08/2023  07:19:47','24/08/2023  07:19:47','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6935d8cb-147d-42eb-83f6-38085d8e5465','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 4745, "city": "city 26", "omnia": "Yes", "caseId": "e4dd4cc5-77f7-4c63-afe9-7917ee0435e5", "credit": 864, "region": "region 26", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 14689, "prevailingWage": 745, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','25/08/2023  07:25:12','25/08/2023  07:25:12','25/08/2023  07:25:12','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6a5360fd-400a-4ed1-ba4c-800b99981db7','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 6435, "city": "city 24", "omnia": "No", "caseId": "32dcd94b-3de8-4782-8ead-cfa8a8189fa7", "credit": 23, "region": "region 24", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 18769, "prevailingWage": 2345, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','26/08/2023  07:24:39','26/08/2023  07:24:39','26/08/2023  07:24:39','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6affc15b-2d75-4be6-aa27-564966246403','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 996, "city": "city 5", "omnia": "Yes", "caseId": "c805fe68-a656-4b74-9a35-2779148863c8", "credit": 2314, "region": "region 5", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 23665, "prevailingWage": 433, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','27/08/2023  07:13:43','27/08/2023  07:13:43','27/08/2023  07:13:43','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6b1ca70f-6b9a-4a4e-97fd-9f0bb73efd43','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','28/08/2023  07:20:23','28/08/2023  07:20:23','28/08/2023  07:20:23','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6bc1253c-2ebe-47e3-b69b-f8bcf07c5201','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 2453, "city": "city 16", "omnia": "Yes", "caseId": "15ee389b-0539-4a8c-b773-7515adc8c223", "credit": 425, "region": "region 16", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 6583, "prevailingWage": 136, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',FALSE,FALSE,FALSE,NULL,NULL,NULL,'29/08/2023  07:34:20',NULL,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6c4fdd92-c819-4a16-a809-09abfe41d185','66208b54-fd31-4c84-ac95-7d16e04057ce','{"bond": 7456, "city": "city 9", "omnia": "Yes", "caseId": "30bc8170-1377-419d-ae2a-89057d91e842", "credit": 746, "region": "region 9", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 18698, "prevailingWage": 535, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','29/08/2023  07:17:35','29/08/2023  07:17:35','29/08/2023  07:17:35','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6c66319b-7768-4b6f-8850-e8674c2fdcd4','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 74, "city": "city 2", "omnia": "No", "caseId": "9d51bd7f-d523-49dc-a974-b377b8a21c26", "credit": 5345, "region": "region 2", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 21000, "prevailingWage": 6345, "badgingRequired": "No", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','30/08/2023  07:12:04','30/08/2023  07:12:04','30/08/2023  07:12:04','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6dcec750-1165-4abe-bbd6-8f90bc348d90','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 3132, "city": "city 28", "omnia": "Yes", "caseId": "47f9f723-54a9-431f-8509-3607f5c4801a", "credit": 1111, "region": "region 28", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 4158, "prevailingWage": 945, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','31/08/2023  07:27:10','31/08/2023  07:27:10','31/08/2023  07:27:10','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('73d3b0bf-0aba-4ce2-9525-0fe7f316193c','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 3435, "city": "city 18", "omnia": "No", "caseId": "bc33d4f5-7091-4fa2-822d-8ba85d99129e", "credit": 346, "region": "region 18", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 17987, "prevailingWage": 1134, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','01/08/2023  07:35:11','01/08/2023  07:35:11','01/08/2023  07:35:11','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('752c7126-9b98-4746-8f6c-a7b04fd79abe','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 6435, "city": "city 24", "omnia": "No", "caseId": "32dcd94b-3de8-4782-8ead-cfa8a8189fa7", "credit": 23, "region": "region 24", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 18769, "prevailingWage": 2345, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','02/08/2023  07:24:44','02/08/2023  07:24:44','02/08/2023  07:24:44','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('762aaa72-a9e0-4f43-b9a4-005e23632096','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 3435, "city": "city 18", "omnia": "No", "caseId": "bc33d4f5-7091-4fa2-822d-8ba85d99129e", "credit": 346, "region": "region 18", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 17987, "prevailingWage": 1134, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',FALSE,FALSE,FALSE,NULL,NULL,NULL,'03/08/2023  07:35:11',NULL,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('76e0d696-7d9a-471e-be76-203f86214ecb','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 4356, "city": "city 11", "omnia": "Yes", "caseId": "2461191a-ca2d-4840-aeb9-0b7eb87cfd31", "credit": 2553, "region": "region 11", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 48578, "prevailingWage": 34, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','03/08/2023  07:18:36','03/08/2023  07:18:36','03/08/2023  07:18:36','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('7857b281-1747-4ec6-a127-b7e941864028','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 3263, "city": "city 10", "omnia": "Yes", "caseId": "eda2d833-f6b5-4209-ba31-b7782a104f22", "credit": 4547, "region": "region 10", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 36578, "prevailingWage": 63, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','04/08/2023  07:17:56','04/08/2023  07:17:56','04/08/2023  07:17:56','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('78bdba1b-c60a-45aa-8aa5-514140675df4','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 6556.0, "city": "city 8", "omnia": "No", "caseId": "2f3e2095-4aff-440f-bb75-a98097322d88", "credit": 454.0, "region": "region 8", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 2658.0, "prevailingWage": 364.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','05/08/2023  07:16:44','05/08/2023  07:16:44','05/08/2023  07:16:44','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('7a7c328d-7947-4273-94a5-fd2e56157cb9','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 647.0, "city": "city 25", "omnia": "Yes", "caseId": "8377429a-1f84-4bfa-b94d-a73d5cf3be0e", "credit": 646.0, "region": "region 25", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 24589.0, "prevailingWage": 1232.0, "badgingRequired": "No", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',FALSE,FALSE,FALSE,NULL,NULL,NULL,'06/08/2023  07:35:39',NULL,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('7ba42c4d-2955-4a7d-a28a-714799b29afa','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 632, "city": "city 21", "omnia": "Yes", "caseId": "30bbd0f6-6b8f-4364-9b87-5e8bac84c504", "credit": 2434, "region": "region 21", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 29878, "prevailingWage": 633, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','06/08/2023  07:22:32','06/08/2023  07:22:32','06/08/2023  07:22:32','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('7d0c505f-de16-4f5d-be5b-1bcbc076afaa','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 4745, "city": "city 26", "omnia": "Yes", "caseId": "e4dd4cc5-77f7-4c63-afe9-7917ee0435e5", "credit": 864, "region": "region 26", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 14689, "prevailingWage": 745, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','07/08/2023  07:25:09','07/08/2023  07:25:09','07/08/2023  07:25:09','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('814d6290-81ab-479d-b26b-66a9dc58a7b9','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 744, "city": "city 1", "omnia": "Yes", "caseId": "6b9f211e-c70c-42d2-b663-a236313526cd", "credit": 643, "region": "region 1", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 15000, "prevailingWage": 234, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','08/08/2023  07:11:45','08/08/2023  07:11:45','08/08/2023  07:11:45','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('81afa692-f0c0-4528-a30d-c825db97c666','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 6454, "city": "city 6", "omnia": "Yes", "caseId": "926bdf7e-c6e4-460b-b33a-28c5bb890a44", "credit": 2452, "region": "region 6", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 598, "prevailingWage": 78, "badgingRequired": "No", "operationReview": "rejected", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','09/08/2023  07:32:49','09/08/2023  07:32:49','09/08/2023  07:32:49','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('82122481-0bd3-4604-87c3-6ba8e176d4d6','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 74, "city": "city 2", "omnia": "No", "caseId": "9d51bd7f-d523-49dc-a974-b377b8a21c26", "credit": 5345, "region": "region 2", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 21000, "prevailingWage": 6345, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','10/08/2023  07:12:06','10/08/2023  07:12:06','10/08/2023  07:12:06','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('8504aa21-b6e3-450c-a5e3-0b168593f2f9','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 644, "city": "city 23", "omnia": "Yes", "caseId": "fd8a6c9c-82aa-4570-8c8a-731a2687ff69", "credit": 7454, "region": "region 23", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 13568, "prevailingWage": 1254, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','11/08/2023  07:23:49','11/08/2023  07:23:49','11/08/2023  07:23:49','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('85921adb-cd04-4323-88f1-94ed45095799','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','12/08/2023  07:19:32','12/08/2023  07:19:32','12/08/2023  07:19:32','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('86b7f6a2-89d7-49f0-8633-49227a7e78f1','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 2356, "city": "city 29", "omnia": "Yes", "caseId": "fa164068-083a-4558-823d-ff6839b8cf8c", "credit": 4243, "region": "region 29", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 29874, "prevailingWage": 735, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','13/08/2023  07:31:46','13/08/2023  07:31:46','13/08/2023  07:31:46','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('891a65cf-aa1d-4809-8b28-e76b6fb60dad','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 6456.0, "city": "city 12", "omnia": "No", "caseId": "21ab4dec-6c0e-44c8-ad87-beb989f53d3b", "credit": 637.0, "region": "region 12", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 11689.0, "prevailingWage": 4525.0, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','14/08/2023  07:19:07','14/08/2023  07:19:07','14/08/2023  07:19:07','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('8a564001-af09-4a3b-b8bf-64ce17f398ba','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 346.0, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346.0, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 544.0, "prevailingWage": 734.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','15/08/2023  07:19:28','15/08/2023  07:19:28','15/08/2023  07:19:28','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('8a97294c-6983-4f81-b337-3fe9d53a98e2','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 452.0, "city": "city 27", "omnia": "Yes", "caseId": "3e4082ab-9d92-4803-b13d-8c9d6cd6ebb6", "credit": 774.0, "region": "region 27", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 50556.0, "prevailingWage": 346.0, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','16/09/2023  07:25:45','16/09/2023  07:25:45','16/09/2023  07:25:45','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('8c66a7a8-296e-4bf5-b7cc-b4fdc41e46d5','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','17/09/2023  07:20:21','17/09/2023  07:20:21','17/09/2023  07:20:21','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('8d765d1d-aa64-4fa7-a1bd-f2c5ba234dec','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 6454, "city": "city 6", "omnia": "Yes", "caseId": "926bdf7e-c6e4-460b-b33a-28c5bb890a44", "credit": 2452, "region": "region 6", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 598, "prevailingWage": 78, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',FALSE,FALSE,FALSE,NULL,NULL,NULL,'18/09/2023  07:32:49',NULL,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('8e983630-6c3c-468c-8042-d630c1bbde74','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 2434, "city": "city 7", "omnia": "Yes", "caseId": "afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f", "credit": 3634, "region": "region 7", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 29365, "prevailingWage": 68, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','18/09/2023  07:15:22','18/09/2023  07:15:22','18/09/2023  07:15:22','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('923d1077-0157-4c7b-98c1-2711a4dda153','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 4356.0, "city": "city 11", "omnia": "Yes", "caseId": "2461191a-ca2d-4840-aeb9-0b7eb87cfd31", "credit": 2553.0, "region": "region 11", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 48578.0, "prevailingWage": 34.0, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','19/09/2023  07:18:30','19/09/2023  07:18:30','19/09/2023  07:18:30','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('9c763ac8-3d31-493f-80df-556c015cadd0','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','20/09/2023  07:19:50','20/09/2023  07:19:50','20/09/2023  07:19:50','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('9ff52e55-202e-4f59-9dfd-55ece2e6d184','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 644, "city": "city 23", "omnia": "Yes", "caseId": "fd8a6c9c-82aa-4570-8c8a-731a2687ff69", "credit": 7454, "region": "region 23", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 13568, "prevailingWage": 1254, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','21/09/2023  07:23:59','21/09/2023  07:23:59','21/09/2023  07:23:59','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('a07fab6f-9d5d-4462-a11e-ce5b1aade486','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 2535, "city": "city 19", "omnia": "Yes", "caseId": "a423a42b-81ee-481f-ba96-d715703d19b3", "credit": 5735, "region": "region 19", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 24898, "prevailingWage": 823, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','22/09/2023  07:21:29','22/09/2023  07:21:29','22/09/2023  07:21:29','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('a0f6a0ed-1b17-41b0-9cfd-fc0d516a820e','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 743, "city": "city 17", "omnia": "Yes", "caseId": "491daf31-1a1b-44dd-8871-e39a15cc44c1", "credit": 678, "region": "region 17", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 19875, "prevailingWage": 423, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','23/09/2023  07:21:07','23/09/2023  07:21:07','23/09/2023  07:21:07','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('a8c94282-3fbb-4650-b2c1-a1d296037e58','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 924.0, "city": "city 22", "omnia": "Yes", "caseId": "95e0f670-bc4a-44c2-9605-0e6e44062770", "credit": 453.0, "region": "region 22", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 8965.0, "prevailingWage": 1734.0, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','24/09/2023  07:23:13','24/09/2023  07:23:13','24/09/2023  07:23:13','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('aa1bb93b-aa5b-4998-bb48-0e81b2bbd4e9','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 2434, "city": "city 7", "omnia": "Yes", "caseId": "afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f", "credit": 3634, "region": "region 7", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 29365, "prevailingWage": 68, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','25/09/2023  07:15:35','25/09/2023  07:15:35','25/09/2023  07:15:35','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('aa1f4f07-d935-49d2-8f88-277bb0e1ff2f','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 4745, "city": "city 26", "omnia": "Yes", "caseId": "e4dd4cc5-77f7-4c63-afe9-7917ee0435e5", "credit": 864, "region": "region 26", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 14689, "prevailingWage": 745, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','26/09/2023  07:25:01','26/09/2023  07:25:01','26/09/2023  07:25:01','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('abb47661-a057-4708-963a-453b5524c88d','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 3132, "city": "city 28", "omnia": "Yes", "caseId": "47f9f723-54a9-431f-8509-3607f5c4801a", "credit": 1111, "region": "region 28", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 4158, "prevailingWage": 945, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','27/09/2023  07:27:05','27/09/2023  07:27:05','27/09/2023  07:27:05','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('ac5f8227-3930-492b-a807-50130d997478','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 744, "city": "city 1", "omnia": "Yes", "caseId": "6b9f211e-c70c-42d2-b663-a236313526cd", "credit": 643, "region": "region 1", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 15000, "prevailingWage": 234, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','28/09/2023  07:11:38','28/09/2023  07:11:38','28/09/2023  07:11:38','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('adf1e263-0380-497f-a9b7-18dc675dc925','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 6556, "city": "city 8", "omnia": "No", "caseId": "2f3e2095-4aff-440f-bb75-a98097322d88", "credit": 454, "region": "region 8", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 2658, "prevailingWage": 364, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','29/09/2023  07:16:52','29/09/2023  07:16:52','29/09/2023  07:16:52','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('aef83edb-5419-4488-a811-8af3580bf926','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 5236.0, "city": "city 3", "omnia": "Yes", "caseId": "6d8600aa-91f9-47fc-a52c-9e1ef503c418", "credit": 242.0, "region": "region 3", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 5985.0, "prevailingWage": 645.0, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','30/09/2023  07:12:31','30/09/2023  07:12:31','30/09/2023  07:12:31','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('afdc2576-a404-4c71-bb1c-3d00e2c02579','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 7456.0, "city": "city 9", "omnia": "Yes", "caseId": "30bc8170-1377-419d-ae2a-89057d91e842", "credit": 746.0, "region": "region 9", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 18698.0, "prevailingWage": 535.0, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','01/09/2023  07:17:28','01/09/2023  07:17:28','01/09/2023  07:17:28','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('b54889de-4758-40ee-9824-926821d3c2a0','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 5342.0, "city": "city 30", "omnia": "Yes", "caseId": "a74c8a43-9eec-4693-802b-ff5550a9aa4d", "credit": 3234.0, "region": "region 30", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 12001.0, "prevailingWage": 345.0, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',FALSE,FALSE,FALSE,NULL,NULL,NULL,'02/09/2023  07:35:47',NULL,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('b85fb8c0-4167-43fe-a80f-f39cda21c2a2','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 996, "city": "city 5", "omnia": "Yes", "caseId": "c805fe68-a656-4b74-9a35-2779148863c8", "credit": 2314, "region": "region 5", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 23665, "prevailingWage": 433, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','02/09/2023  07:13:45','02/09/2023  07:13:45','02/09/2023  07:13:45','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('bb094678-8e67-4799-bfd1-e006cb6ad65e','66208b54-fd31-4c84-ac95-7d16e04057ce','{"bond": 7456, "city": "city 4", "omnia": "Yes", "caseId": "b9f0cd00-c8b4-4ad3-8119-79bb934592ee", "credit": 3635, "region": "region 4", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 9885, "prevailingWage": 784, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','03/09/2023  07:13:19','03/09/2023  07:13:19','03/09/2023  07:13:19','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('bb71c584-ff9a-4d78-995d-f72c2291caa9','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 6556, "city": "city 8", "omnia": "No", "caseId": "2f3e2095-4aff-440f-bb75-a98097322d88", "credit": 454, "region": "region 8", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "Yes", "estimatedCost": 2658, "prevailingWage": 364, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','04/09/2023  07:16:57','04/09/2023  07:16:57','04/09/2023  07:16:57','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('bc245016-41d5-4d98-995f-880be8ae2b6c','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 4745, "city": "city 26", "omnia": "Yes", "caseId": "e4dd4cc5-77f7-4c63-afe9-7917ee0435e5", "credit": 864, "region": "region 26", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 14689, "prevailingWage": 745, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','05/09/2023  07:25:06','05/09/2023  07:25:06','05/09/2023  07:25:06','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('bc68e150-b61a-4558-9b88-2f59e3188e37','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 744, "city": "city 1", "omnia": "Yes", "caseId": "6b9f211e-c70c-42d2-b663-a236313526cd", "credit": 643, "region": "region 1", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 15000, "prevailingWage": 234, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','06/09/2023  07:11:41','06/09/2023  07:11:41','06/09/2023  07:11:41','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('bcefa893-c085-4b24-9c57-dc784faba542','66208b54-fd31-4c84-ac95-7d16e04057ce','{"bond": 6456, "city": "city 12", "omnia": "No", "caseId": "21ab4dec-6c0e-44c8-ad87-beb989f53d3b", "credit": 637, "region": "region 12", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 11689, "prevailingWage": 4525, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','07/09/2023  07:19:12','07/09/2023  07:19:12','07/09/2023  07:19:12','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('beee741b-7984-44f1-b8cc-e899d4a40cb4','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 632, "city": "city 21", "omnia": "Yes", "caseId": "30bbd0f6-6b8f-4364-9b87-5e8bac84c504", "credit": 2434, "region": "region 21", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 29878, "prevailingWage": 633, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "Yes", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','08/09/2023  07:22:35','08/09/2023  07:22:35','08/09/2023  07:22:35','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('bf27924d-d0a3-41a3-af5f-be8febdf9a89','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 3132, "city": "city 28", "omnia": "Yes", "caseId": "47f9f723-54a9-431f-8509-3607f5c4801a", "credit": 1111, "region": "region 28", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 4158, "prevailingWage": 945, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','09/09/2023  07:26:38','09/09/2023  07:26:38','09/09/2023  07:26:38','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('c18aabc7-609f-44c8-9bc0-d300a69a0752','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 6343, "city": "city 20", "omnia": "Yes", "caseId": "b0300d15-50eb-45b5-a520-cbca07345ec7", "credit": 4656, "region": "region 20", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 5889, "prevailingWage": 346, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','10/09/2023  07:21:56','10/09/2023  07:21:56','10/09/2023  07:21:56','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('c44c3860-c9bb-46aa-87f1-fffe6367310b','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 7456, "city": "city 4", "omnia": "Yes", "caseId": "b9f0cd00-c8b4-4ad3-8119-79bb934592ee", "credit": 3635, "region": "region 4", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 9885, "prevailingWage": 784, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','11/09/2023  07:13:15','11/09/2023  07:13:15','11/09/2023  07:13:15','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('c65694ac-30e0-4b78-a221-9870d57abf07','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 996, "city": "city 5", "omnia": "Yes", "caseId": "c805fe68-a656-4b74-9a35-2779148863c8", "credit": 2314, "region": "region 5", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 23665, "prevailingWage": 433, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','12/09/2023  07:13:40','12/09/2023  07:13:40','12/09/2023  07:13:40','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('c698f0cb-e12b-4ee1-91ba-4840dab1bdef','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 6343, "city": "city 20", "omnia": "Yes", "caseId": "b0300d15-50eb-45b5-a520-cbca07345ec7", "credit": 4656, "region": "region 20", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 5889, "prevailingWage": 346, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','13/09/2023  07:21:58','13/09/2023  07:21:58','13/09/2023  07:21:58','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('c702c94d-0ff1-4e82-94bc-a55970b2db48','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 6343, "city": "city 20", "omnia": "Yes", "caseId": "b0300d15-50eb-45b5-a520-cbca07345ec7", "credit": 4656, "region": "region 20", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 5889, "prevailingWage": 346, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','14/09/2023  07:22:06','14/09/2023  07:22:06','14/09/2023  07:22:06','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('c92f8607-e6ff-4c4a-ad3d-87b645ac2828','66208b54-fd31-4c84-ac95-7d16e04057ce','{"bond": 5236, "city": "city 3", "omnia": "Yes", "caseId": "6d8600aa-91f9-47fc-a52c-9e1ef503c418", "credit": 242, "region": "region 3", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 5985, "prevailingWage": 645, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','15/09/2023  07:12:37','15/09/2023  07:12:37','15/09/2023  07:12:37','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('ca1b349f-124e-4259-8106-4494bab15f4f','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 744, "city": "city 1", "omnia": "Yes", "caseId": "6b9f211e-c70c-42d2-b663-a236313526cd", "credit": 643, "region": "region 1", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 15000, "prevailingWage": 234, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','16/09/2023  07:11:35','16/09/2023  07:11:35','16/09/2023  07:11:35','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('cbdfcb77-9a96-4710-9e69-091b4a751a88','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 6435, "city": "city 24", "omnia": "No", "caseId": "32dcd94b-3de8-4782-8ead-cfa8a8189fa7", "credit": 23, "region": "region 24", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 18769, "prevailingWage": 2345, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','17/09/2023  07:24:34','17/09/2023  07:24:34','17/09/2023  07:24:34','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('cc9dcba0-9f84-4e6e-ba1d-009773e0dd6b','66208b54-fd31-4c84-ac95-7d16e04057ce','{"bond": 924, "city": "city 22", "omnia": "Yes", "caseId": "95e0f670-bc4a-44c2-9605-0e6e44062770", "credit": 453, "region": "region 22", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 8965, "prevailingWage": 1734, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','18/09/2023  07:23:26','18/09/2023  07:23:26','18/09/2023  07:23:26','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('cf829dd0-c18b-42e2-a948-2016d649b387','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 5236, "city": "city 3", "omnia": "Yes", "caseId": "6d8600aa-91f9-47fc-a52c-9e1ef503c418", "credit": 242, "region": "region 3", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 5985, "prevailingWage": 645, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','19/09/2023  07:12:34','19/09/2023  07:12:34','19/09/2023  07:12:34','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('d2821011-ee26-4ef1-81da-fc0f4d64d979','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 6343, "city": "city 20", "omnia": "Yes", "caseId": "b0300d15-50eb-45b5-a520-cbca07345ec7", "credit": 4656, "region": "region 20", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 5889, "prevailingWage": 346, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','20/09/2023  07:22:04','20/09/2023  07:22:04','20/09/2023  07:22:04','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('d2b58296-ff32-47e5-96bc-6d344a902d72','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 2434, "city": "city 7", "omnia": "Yes", "caseId": "afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f", "credit": 3634, "region": "region 7", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 29365, "prevailingWage": 68, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','21/09/2023  07:15:32','21/09/2023  07:15:32','21/09/2023  07:15:32','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('d4db6384-42f4-4a00-9d4a-3d0bbf4060e6','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 7456, "city": "city 9", "omnia": "Yes", "caseId": "30bc8170-1377-419d-ae2a-89057d91e842", "credit": 746, "region": "region 9", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 18698, "prevailingWage": 535, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','22/09/2023  07:17:33','22/09/2023  07:17:33','22/09/2023  07:17:33','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('d5f15fb0-d951-4967-bf38-c9eb0d918778','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 346, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 544, "prevailingWage": 734, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','23/09/2023  07:20:25','23/09/2023  07:20:25','23/10/2023  07:20:25','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('d65514bb-06dd-4a28-a240-df67633919d4','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 632, "city": "city 21", "omnia": "Yes", "caseId": "30bbd0f6-6b8f-4364-9b87-5e8bac84c504", "credit": 2434, "region": "region 21", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 29878, "prevailingWage": 633, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','24/10/2023  07:22:40','24/10/2023  07:22:40','24/10/2023  07:22:40','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('d67eeab7-e4ad-4c65-9afc-0564bd02674c','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 6454.0, "city": "city 6", "omnia": "Yes", "caseId": "926bdf7e-c6e4-460b-b33a-28c5bb890a44", "credit": 2452.0, "region": "region 6", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 598.0, "prevailingWage": 78.0, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','25/10/2023  07:32:40','25/10/2023  07:32:40','25/10/2023  07:32:40','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('d7ebff81-666e-4ee0-8566-3e94dd4631a6','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 74, "city": "city 2", "omnia": "No", "caseId": "9d51bd7f-d523-49dc-a974-b377b8a21c26", "credit": 5345, "region": "region 2", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 21000, "prevailingWage": 6345, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','26/10/2023  07:12:01','26/10/2023  07:12:01','26/10/2023  07:12:01','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('d8d10110-a9ea-4f72-baf0-ee92aeb72393','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 346.0, "city": "city 14", "omnia": "Yes", "caseId": "9c21ac60-b016-4c8a-9c78-3febae04e70e", "credit": 346.0, "region": "region 14", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "Yes", "estimatedCost": 544.0, "prevailingWage": 734.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','27/10/2023  07:20:13','27/10/2023  07:20:13','27/10/2023  07:20:13','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('da33205a-ec56-4acd-8695-5874165bcd16','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 996, "city": "city 5", "omnia": "Yes", "caseId": "c805fe68-a656-4b74-9a35-2779148863c8", "credit": 2314, "region": "region 5", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 23665, "prevailingWage": 433, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','28/10/2023  07:13:51','28/10/2023  07:13:51','28/10/2023  07:13:51','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('da737183-3aef-4278-8c00-18089bc1dfd1','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 2434, "city": "city 7", "omnia": "Yes", "caseId": "afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f", "credit": 3634, "region": "region 7", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 29365, "prevailingWage": 68, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','29/10/2023  07:15:30','29/10/2023  07:15:30','29/10/2023  07:15:30','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('e09f2ca6-30e6-4036-bd7c-ebf00dee3a08','cba0ddeb-4372-47f8-a75d-5c1bb0605e04','{"bond": 452, "city": "city 27", "omnia": "Yes", "caseId": "3e4082ab-9d92-4803-b13d-8c9d6cd6ebb6", "credit": 774, "region": "region 27", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 50556, "prevailingWage": 346, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','30/10/2023  07:25:56','30/10/2023  07:25:56','30/10/2023  07:25:56','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('e49cc72c-0627-4394-b95f-b75362936002','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 644.0, "city": "city 23", "omnia": "Yes", "caseId": "fd8a6c9c-82aa-4570-8c8a-731a2687ff69", "credit": 7454.0, "region": "region 23", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 13568.0, "prevailingWage": 1254.0, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','01/10/2023  07:23:46','01/10/2023  07:23:46','01/10/2023  07:23:46','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('e5632faa-93af-4aaf-9286-0447546647fa','c1702bc3-9e3b-417c-8d89-828c540f608a','{"bond": 744, "city": "city 1", "omnia": "Yes", "caseId": "6b9f211e-c70c-42d2-b663-a236313526cd", "credit": 643, "region": "region 1", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 15000, "prevailingWage": 234, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','02/10/2023  07:11:48','02/10/2023  07:11:48','02/10/2023  07:11:48','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('e7227096-2435-459b-975a-ee636f406c9d','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 743, "city": "city 17", "omnia": "Yes", "caseId": "491daf31-1a1b-44dd-8871-e39a15cc44c1", "credit": 678, "region": "region 17", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 19875, "prevailingWage": 423, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','03/10/2023  07:20:58','03/10/2023  07:20:58','03/10/2023  07:20:58','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('e77e8f95-436f-4f45-81bd-ff6baf19c331','54c7a131-835d-4090-a348-64f1e1bda279','{"bond": 3263.0, "city": "city 10", "omnia": "Yes", "caseId": "eda2d833-f6b5-4209-ba31-b7782a104f22", "credit": 4547.0, "region": "region 10", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 36578.0, "prevailingWage": 63.0, "badgingRequired": "No", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','04/10/2023  07:17:54','04/10/2023  07:17:54','04/10/2023  07:17:54','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('ec740171-8023-46e6-8407-2fff623ee5a8','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 924, "city": "city 22", "omnia": "Yes", "caseId": "95e0f670-bc4a-44c2-9605-0e6e44062770", "credit": 453, "region": "region 22", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "No", "estimatedCost": 8965, "prevailingWage": 1734, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','05/10/2023  07:23:24','05/10/2023  07:23:24','05/10/2023  07:23:24','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('ed2edef7-d0c4-42ec-913e-bc9312165978','cba0ddeb-4372-47f8-a75d-5c1bb0605e04','{"bond": 632, "city": "city 21", "omnia": "Yes", "caseId": "30bbd0f6-6b8f-4364-9b87-5e8bac84c504", "credit": 2434, "region": "region 21", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 29878, "prevailingWage": 633, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "Yes", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','06/10/2023  07:22:37','06/10/2023  07:22:37','06/10/2023  07:22:37','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('ed714369-8f5e-4dcf-b625-8633d5495568','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 743, "city": "city 17", "omnia": "Yes", "caseId": "491daf31-1a1b-44dd-8871-e39a15cc44c1", "credit": 678, "region": "region 17", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 19875, "prevailingWage": 423, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','07/10/2023  07:21:01','07/10/2023  07:21:01','07/10/2023  07:21:01','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('f6dca089-708e-495a-b79a-0ad17dfcab2d','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 6435, "city": "city 24", "omnia": "No", "caseId": "32dcd94b-3de8-4782-8ead-cfa8a8189fa7", "credit": 23, "region": "region 24", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 18769, "prevailingWage": 2345, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','08/10/2023  07:24:37','08/10/2023  07:24:37','08/10/2023  07:24:37','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('f93bedc9-f077-42bc-b3af-f96fc4cf36e9','71dfe89b-11c3-4b85-8f46-3bec932a49c6','{"bond": 452, "city": "city 27", "omnia": "Yes", "caseId": "3e4082ab-9d92-4803-b13d-8c9d6cd6ebb6", "credit": 774, "region": "region 27", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 50556, "prevailingWage": 346, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','09/10/2023  07:25:51','09/10/2023  07:25:51','09/10/2023  07:25:51','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('fa19d415-76f5-478d-973c-5580f3e5d28e','1234adec-f678-4d3c-9c60-de6f4311f89e','{"bond": 2535, "city": "city 19", "omnia": "Yes", "caseId": "a423a42b-81ee-481f-ba96-d715703d19b3", "credit": 5735, "region": "region 19", "contract": "GSA", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 24898, "prevailingWage": 823, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','10/10/2023  07:21:37','10/10/2023  07:21:37','10/10/2023  07:21:37','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('fe807bae-37e8-40e6-80a5-c8384ad4e914','cba0ddeb-4372-47f8-a75d-5c1bb0605e04','{"bond": 2434, "city": "city 7", "omnia": "Yes", "caseId": "afa3a8da-dc0f-494e-aa9d-e9e9457b3d4f", "credit": 3634, "region": "region 7", "contract": "GSA", "taxExempt": "Yes", "contactType": "contact type 1", "engineering": "Yes", "estimatedCost": 29365, "prevailingWage": 68, "badgingRequired": "Yes", "operationReview": "rejected", "contractApproval": "No", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','11/10/2023  07:15:27','11/10/2023  07:15:27','11/10/2023  07:15:27','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('ff320ad6-a140-4c36-a57b-2e618003e0e7','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 6454, "city": "city 6", "omnia": "Yes", "caseId": "926bdf7e-c6e4-460b-b33a-28c5bb890a44", "credit": 2452, "region": "region 6", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 2", "engineering": "No", "estimatedCost": 598, "prevailingWage": 78, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','12/10/2023  07:32:45','12/10/2023  07:32:45','12/10/2023  07:32:45','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('ffd23a5d-b4a4-4bf5-9e23-afbcef88e2d8','99464e41-c3e0-46a6-94e1-552e7077fa2e','{"bond": 4356, "city": "city 11", "omnia": "Yes", "caseId": "2461191a-ca2d-4840-aeb9-0b7eb87cfd31", "credit": 2553, "region": "region 11", "contract": "MAC", "taxExempt": "Yes", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 48578, "prevailingWage": 34, "badgingRequired": "Yes", "operationReview": "approved", "contractApproval": "No", "marginOverrideRequested": "Yes", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',TRUE,TRUE,FALSE,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9','13/10/2023  07:18:33','13/10/2023  07:18:33','13/10/2023  07:18:33','9fdaed10-dcc2-45fd-98e7-677429b413d9','9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('6a57e8f5-76ec-476c-ae07-281493a883e6','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 6454, "city": "city 6", "omnia": "Yes", "caseId": "eda2d833-f6b5-4209-ba31-b7782a104f22", "credit": 2452, "region": "region 6", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 598, "prevailingWage": 78, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',FALSE,FALSE,FALSE,NULL,NULL,NULL,'18/09/2023  07:32:49',NULL,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('704fd981-9039-4d70-9f16-ff15ffa65048','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 6454, "city": "city 6", "omnia": "Yes", "caseId": "47f9f723-54a9-431f-8509-3607f5c4801a", "credit": 2452, "region": "region 6", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 598, "prevailingWage": 78, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Won", "proposalSent": "Yes", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',FALSE,FALSE,FALSE,NULL,NULL,NULL,'18/09/2023  07:32:49',NULL,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9'),
                                   ('4282576e-c16d-48bd-8097-4438b2a99d3e','f8a27610-689e-447b-bb97-a9950429cd5e','{"bond": 6454, "city": "city 6", "omnia": "Yes", "caseId": "6b9f211e-c70c-42d2-b663-a236313526cd", "credit": 2452, "region": "region 6", "contract": "MAC", "taxExempt": "No", "contactType": "contact type 3", "engineering": "No", "estimatedCost": 598, "prevailingWage": 78, "badgingRequired": "No", "operationReview": "approved", "contractApproval": "Yes", "marginOverrideRequested": "No", "bidOutcome": "Lost", "proposalSent": "No", "proposalSentDate": "2023-10-15"}','[{"field": "region", "label": "Region", "visible": true, "editable": true}, {"field": "contactType", "label": "Contact Type", "visible": true, "editable": true}, {"field": "contractApproval", "label": "Contract Approval", "visible": true, "editable": true}, {"field": "contract", "label": "GSA or MAC Contract", "visible": true, "editable": true}, {"field": "omnia", "label": "OMNIA", "visible": true, "editable": true}, {"field": "marginOverrideRequested", "label": "Margin Override Requested", "visible": true, "editable": true}, {"field": "taxExempt", "label": "Tax Exempt", "visible": true, "editable": true}, {"field": "prevailingWage", "label": "Prevailing Wage", "visible": true, "editable": true}, {"field": "credit", "label": "Credit", "visible": true, "editable": true}, {"field": "bond", "label": "Bond", "visible": true, "editable": true}, {"field": "engineering", "label": "Engineering", "visible": true, "editable": true}, {"field": "badgingRequired", "label": "Badging Required", "visible": true, "editable": true}, {"field": "city", "label": "City of Site", "visible": true, "editable": true}, {"field": "operationReview", "label": "Operation Review", "visible": true, "editable": true}, {"field": "estimatedCost", "label": "Estimated Cost", "visible": true, "editable": true}, {"field": "bidOutcome", "label": "Bid Outcome", "visible": true, "editable": true}, {"field": "proposalSent", "label": "Proposal Sent", "visible": true, "editable": true}, {"field": "proposalSentDate", "label": "Proposal Sent Date", "visible": true, "editable": true}, {"field": "caseId", "label": "Case Id", "visible": true, "editable": true}]',FALSE,FALSE,FALSE,NULL,NULL,NULL,'18/09/2023  07:32:49',NULL,NULL,'9fdaed10-dcc2-45fd-98e7-677429b413d9');

END;
$$;

CREATE OR REPLACE FUNCTION default_data()
  RETURNS void
  LANGUAGE plpgsql
AS
$$
BEGIN
  DELETE FROM users WHERE user_id IN ('12a755f6-cd57-4cc9-8760-fe8845221244',
                                      '3b4cc01e-76b0-4c28-90b6-361bff853d30',
                                      'd1b6c829-59f7-4682-b8ca-08f240c00e7a',
                                      '56a96128-a1be-4046-a7a5-8954adc8eb84',
                                      '8d55ac74-d71f-47ec-8e80-17ead7709a5c',
                                      'e8201944-dfd5-4dc2-850f-5827959ada60');
  TRUNCATE TABLE workflow CASCADE;
  TRUNCATE TABLE case_data CASCADE;
  TRUNCATE TABLE case_data_information CASCADE;
  TRUNCATE TABLE case_status_history;
END;
$$;

CREATE OR REPLACE FUNCTION data_load(opt varchar(15))
  RETURNS void
  LANGUAGE plpgsql
AS
$$
BEGIN
  CASE
    WHEN opt = 'D1' THEN
      PERFORM d1();
    ELSE
      PERFORM default_data();
    END CASE;
END;
$$;
