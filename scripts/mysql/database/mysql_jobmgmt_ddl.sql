CREATE TABLE roles_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE case_status_lov
(
  code        VARCHAR(8)    NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE case_type_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE audit_action_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE audit_entity_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  B<PERSON><PERSON><PERSON>N       NOT NULL DEFAULT false
);

CREATE TABLE notification_type_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE system_skin_lov
(
  code        INTEGER       NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE system_theme_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE system_files_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  file        LONGBLOB      NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE data_set_lov
(
  code            VARCHAR(15)   NOT NULL PRIMARY KEY,
  display_order   INTEGER       NOT NULL,
  description     VARCHAR(2047) NOT NULL,
  java_class_name VARCHAR(255)  NULL,
  deprecated      BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE uploaded_data_status_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE task_type_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  data        JSON          NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE demo_lov
(
  code        VARCHAR(15)   NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated  BOOLEAN       NOT NULL DEFAULT false
);

CREATE TABLE hierarchy_level
(
  level          INTEGER            NOT NULL PRIMARY KEY,
  hierarchy_name VARCHAR(25) UNIQUE NOT NULL,
  description    VARCHAR(255)       NOT NULL
);

CREATE TABLE hierarchy_node
(
  node_id         INTEGER     NOT NULL PRIMARY KEY,
  parent_id       INTEGER     NULL,
  level           INTEGER     NOT NULL,
  hierarchy_value VARCHAR(25) NOT NULL,
  CONSTRAINT fk_level
    FOREIGN KEY (level) REFERENCES hierarchy_level (level)
);

CREATE TABLE `system`
(
  id     VARCHAR(15) PRIMARY KEY,
  skin   INTEGER     NULL,
  theme  VARCHAR(15) NULL,
  colors VARCHAR(32) NULL,
  logo   MEDIUMTEXT  NULL
);

CREATE TABLE users
(
  user_id          BINARY(16)   PRIMARY KEY,
  email            VARCHAR(320) NOT NULL UNIQUE,
  is_active        BOOLEAN      NOT NULL DEFAULT false,
  first_name       VARCHAR(50)  NOT NULL,
  last_name        VARCHAR(50)  NOT NULL,
  profile_image    LONGBLOB     NULL,
  title            VARCHAR(15)  NULL,
  hierarchy_id     INTEGER      NULL,
  effective_date   TIMESTAMP    NOT NULL,
  termination_date TIMESTAMP    NULL,
  CONSTRAINT fk_hierarchy_u
    FOREIGN KEY (hierarchy_id) REFERENCES hierarchy_node (node_id) ON DELETE CASCADE,
  CONSTRAINT ck_users_termination_date
    CHECK (termination_date IS NULL OR termination_date >= effective_date)
);

CREATE TABLE role
(
  role_id BINARY(16) PRIMARY KEY,
  name        VARCHAR(25)   NOT NULL UNIQUE,
  department  VARCHAR(15)   NOT NULL,
  superior_id BINARY(16) NULL,
  description VARCHAR(2047) NOT NULL
);

CREATE TABLE user_roles_xref
(
  user_id BINARY(16) NOT NULL,
  role_id BINARY(16) NOT NULL,
  CONSTRAINT pk_user_roles_xref PRIMARY KEY (user_id, role_id),
  CONSTRAINT fk_users_urx
    FOREIGN KEY (user_id) REFERENCES users (user_id) ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT fk_roles
    FOREIGN KEY (role_id) REFERENCES role (role_id) ON DELETE CASCADE
);

CREATE TABLE role_capabilities_xref
(
  role_id BINARY(16) NOT NULL,
  component VARCHAR(100) NOT NULL,
  view      BOOLEAN      NOT NULL,
  edit      BOOLEAN      NOT NULL,
  `create`  BOOLEAN      NOT NULL,
  `delete`  BOOLEAN      NOT NULL,
  CONSTRAINT pk_role_capabilities_xref PRIMARY KEY (role_id, component),
  CONSTRAINT fk_role
    FOREIGN KEY (role_id) REFERENCES role (role_id) ON DELETE CASCADE
);

CREATE TABLE user_contact_xref
(
  user_id BINARY(16) NOT NULL PRIMARY KEY,
  phone   VARCHAR(15) NULL UNIQUE,
  mobile  VARCHAR(15) NULL UNIQUE,
  address VARCHAR(95) NULL,
  CONSTRAINT fk_users_ucx
    FOREIGN KEY (user_id) REFERENCES users (user_id)
);

CREATE TABLE user_config
(
  user_id BINARY(16) NOT NULL PRIMARY KEY,
  dashboard JSON NULL,
  CONSTRAINT fk_users_uc
    FOREIGN KEY (user_id) REFERENCES users (user_id)
);

CREATE TABLE user_notifications_xref
(
  id           INTEGER     NOT NULL,
  user_id BINARY(16) NOT NULL,
  type         VARCHAR(15) NOT NULL,
  body         TEXT,
  url          VARCHAR(2047),
  created_date TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT pk_user_notification_xref PRIMARY KEY (user_id, id),
  CONSTRAINT fk_notification_type_lov_unx
    FOREIGN KEY (type) REFERENCES notification_type_lov (code)
);

CREATE TABLE user_notifications_config_xref
(
  user_id BINARY(16) NOT NULL,
  type   VARCHAR(15) NOT NULL,
  object_id BINARY(16) NOT NULL,
  notify BOOLEAN     NOT NULL,
  CONSTRAINT pk_user_notification_config_xref PRIMARY KEY (user_id, type),
  CONSTRAINT fk_users_uncx
    FOREIGN KEY (user_id) REFERENCES users (user_id),
  CONSTRAINT fk_notification_type_lov_uncx
    FOREIGN KEY (type) REFERENCES notification_type_lov (code)
);

CREATE TABLE case_data_information
(
  case_id BINARY(16) NOT NULL PRIMARY KEY,
  region                    VARCHAR(100) NULL,
  contact_type              VARCHAR(100) NULL,
  contract_approval         VARCHAR(100) NULL,
  contract                  VARCHAR(100) NULL,
  omnia                     VARCHAR(100) NULL,
  margin_override_requested VARCHAR(100) NULL,
  tax_exempt                VARCHAR(100) NULL,
  prevailing_wage           DOUBLE       NULL,
  credit                    DOUBLE       NULL,
  bond                      DOUBLE       NULL,
  engineering               VARCHAR(100) NULL,
  badging_required          VARCHAR(100) NULL,
  city                      VARCHAR(100) NULL,
  operation_review          VARCHAR(100) NULL,
  estimated_cost            DOUBLE       NULL,
  bid_outcome               VARCHAR(100) NULL,
  proposal_sent             VARCHAR(100) NULL,
  proposal_sent_date        VARCHAR(100) NULL
);

CREATE TABLE case_data
(
  case_id BINARY(16) PRIMARY KEY,
  name               VARCHAR(25) NOT NULL UNIQUE,
  hierarchy_id       INTEGER     NOT NULL,
  opened_by BINARY(16) NOT NULL,
  opened_date        TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
  description        VARCHAR(2047),
  location           VARCHAR(90),
  current_status     VARCHAR(15) NOT NULL DEFAULT 'Open',
  status_modified_by BINARY(16) NOT NULL,
  status_update_date TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_case_data_opened
    FOREIGN KEY (opened_by) REFERENCES users (user_id) ON DELETE CASCADE,
  CONSTRAINT fk_case_data_modified
    FOREIGN KEY (status_modified_by) REFERENCES users (user_id) ON DELETE CASCADE,
  CONSTRAINT fk_case_status_lov
    FOREIGN KEY (current_status) REFERENCES case_status_lov (code),
  CONSTRAINT fk_hierarchy_cs
    FOREIGN KEY (hierarchy_id) REFERENCES hierarchy_node (node_id),
  CONSTRAINT fk_data_information
    FOREIGN KEY (case_id) REFERENCES case_data_information (case_id)
);

CREATE TABLE case_status_transition
(
  id     INTEGER AUTO_INCREMENT NOT NULL PRIMARY KEY,
  source VARCHAR(8) NOT NULL,
  target VARCHAR(8) NOT NULL,
  CONSTRAINT fk_case_status_source
    FOREIGN KEY (source) REFERENCES case_status_lov (code),
  CONSTRAINT fk_case_status_target
    FOREIGN KEY (target) REFERENCES case_status_lov (code)
);

CREATE TABLE case_type_xref
(
  case_id BINARY(16) NOT NULL,
  case_type VARCHAR(15) NOT NULL,
  CONSTRAINT pk_case_type_xref PRIMARY KEY (case_id, case_type),
  CONSTRAINT fk_case_data
    FOREIGN KEY (case_id) REFERENCES case_data (case_id),
  CONSTRAINT fk_case_type
    FOREIGN KEY (case_type) REFERENCES case_type_lov (code)
);

CREATE TABLE case_status_history
(
  id                 INTEGER AUTO_INCREMENT NOT NULL PRIMARY KEY,
  case_id BINARY(16) NOT NULL,
  status             VARCHAR(15) NOT NULL,
  status_update_date TIMESTAMP   NOT NULL
);

CREATE TABLE uploaded_data
(
  data_set_code VARCHAR(15)   NOT NULL PRIMARY KEY,
  file_name     VARCHAR(2047) NULL,
  status_code   VARCHAR(15)   NOT NULL,
  uploaded_date TIMESTAMP     NULL,
  CONSTRAINT fk_uploaded_data_code
    FOREIGN KEY (data_set_code) REFERENCES data_set_lov (code),
  CONSTRAINT fk_uploaded_data_status
    FOREIGN KEY (status_code) REFERENCES uploaded_data_status_lov (code)
);

CREATE TABLE service_call_log
(
  id BINARY(16) NOT NULL PRIMARY KEY,
  accept_type       TEXT,
  class_name        TEXT,
  client_id         INTEGER,
  content_type      TEXT,
  elapsed_time_ms   BIGINT,
  end_time          TIMESTAMP,
  error_msg         TEXT,
  host_name         TEXT,
  http_action       TEXT,
  log_response      BOOLEAN NOT NULL,
  method_name       TEXT,
  path_parms        JSON,
  query_parms       JSON,
  remote_ip         TEXT,
  req_header_parms  JSON,
  resource          TEXT,
  resp_header_parms JSON,
  response_code     INTEGER NOT NULL,
  request_body      TEXT,
  response_body     TEXT,
  stack_trace       TEXT,
  start_time        TIMESTAMP,
  submit_time       TIMESTAMP,
  user_name         TEXT
);

CREATE TABLE audit_log
(
  audit_id BINARY(16) NOT NULL PRIMARY KEY,
  user_id BINARY(16) NOT NULL,
  action_code  VARCHAR(15) NOT NULL,
  entity_code  TEXT        NOT NULL,
  entity_key   TEXT        NOT NULL,
  audit_date   TIMESTAMP   NOT NULL,
  entity_value JSON        NULL
);

CREATE TABLE workflow
(
  id BINARY(16) NOT NULL PRIMARY KEY,
  workflow          VARCHAR(100) UNIQUE NOT NULL,
  description       VARCHAR(2047)       NULL,
  in_use            BOOLEAN DEFAULT FALSE,
  completed         BOOLEAN DEFAULT FALSE,
  created_by_system BOOLEAN DEFAULT FALSE
);

CREATE TABLE workflow_task_type
(
  task_id BINARY(16) NOT NULL PRIMARY KEY,
  workflow_id BINARY(16) NOT NULL,
  task_type_id                    VARCHAR(15)    NULL,
  task_name                       VARCHAR(100)   NOT NULL,
  fields                          JSON           NULL,
  approval_task                   BOOLEAN        NULL,
  has_email                       BOOLEAN        NOT NULL DEFAULT FALSE,
  email_to                        TEXT           NULL,
  email_subject                   VARCHAR(60)    NULL,
  email_body                      VARCHAR(10000) NULL,
  trigger_workflow                BOOLEAN        NOT NULL DEFAULT FALSE,
  trigger_workflow_id BINARY(16) NULL,
  has_directory                   BOOLEAN        NOT NULL DEFAULT FALSE,
  service_level_agreement         INT            NOT NULL DEFAULT 3,
  service_level_agreement_warning INT            NOT NULL DEFAULT 2,
  description                     VARCHAR(2047)  NULL,
  UNIQUE KEY unique_task_name (workflow_id, task_name),
  FOREIGN KEY (workflow_id) REFERENCES workflow (id),
  FOREIGN KEY (trigger_workflow_id) REFERENCES workflow (id),
  FOREIGN KEY (task_type_id) REFERENCES task_type_lov (code)
);

CREATE TABLE workflow_task
(
  id BINARY(16) NOT NULL PRIMARY KEY,
  task_type_id BINARY(16) NOT NULL,
  value          JSON          NULL,
  req_value      JSON          NOT NULL,
  done           BOOLEAN       NOT NULL,
  approved       BOOLEAN       NULL,
  deleted        BOOLEAN       NULL,
  justification  VARCHAR(2047) NULL,
  worked_by BINARY(16) NULL,
  completed_date TIMESTAMP     NULL,
  created_date   TIMESTAMP     NOT NULL,
  modified_date  TIMESTAMP     NULL,
  modified_by BINARY(16) NULL,
  created_by BINARY(16) NOT NULL,
  FOREIGN KEY (task_type_id) REFERENCES workflow_task_type (task_id),
  FOREIGN KEY (worked_by) REFERENCES users (user_id)
);

CREATE TABLE role_workflow_task_xref
(
  role_id BINARY(16) NOT NULL,
  task_id BINARY(16) NOT NULL,
  PRIMARY KEY (role_id, task_id),
  FOREIGN KEY (role_id) REFERENCES role (role_id),
  FOREIGN KEY (task_id) REFERENCES workflow_task_type (task_id) ON DELETE CASCADE
);

CREATE TABLE workflow_task_type_relation
(
  id                  INT         NOT NULL,
  workflow_id BINARY(16) NOT NULL,
  input BINARY(16) NOT NULL,
  output BINARY(16) NOT NULL,
  priority            INT         NOT NULL,
  relevant_data_field VARCHAR(50) NULL,
  relevant_data_value VARCHAR(50) NULL,
  comparison_operator VARCHAR(2)  NULL,
  PRIMARY KEY (id, workflow_id),
  FOREIGN KEY (workflow_id) REFERENCES workflow (id),
  FOREIGN KEY (input) REFERENCES workflow_task_type (task_id),
  FOREIGN KEY (output) REFERENCES workflow_task_type (task_id)
);
