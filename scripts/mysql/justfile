set dotenv-load
set windows-shell := ["powershell.exe", "-<PERSON><PERSON><PERSON>", "-Command"]

# Default value for runMode
default:='default'

# Enviroment variables
PROJECT_NAME  := env_var('PROJECT_NAME')

# Start server containers, populate db
all runMode=default: (start runMode) (initdb runMode)

# Start server containers
start runMode=default:
    @echo "Starting up containers for {{PROJECT_NAME}} in {{runMode}} mode..."
    @docker-compose pull
    @docker-compose up -d --remove-orphans;

# Stop and remove server containers
destroy: stop
    @echo "Removing containers for {{PROJECT_NAME}}..."
    @docker-compose down -v

# Stop server containers.
stop:
    @echo "Stopping containers for {{PROJECT_NAME}}..."
    @docker-compose stop

# Populate server db;
initdb runMode=default:
    @sleep 10
    @echo "Copying db createa and load scripts to image..."
    @docker cp ./config.cnf {{PROJECT_NAME}}-mysql:/etc
    @docker cp ./database/createUserDB.sh {{PROJECT_NAME}}-mysql:/tmp
    @docker cp ./database/loadDB.sh {{PROJECT_NAME}}-mysql:/tmp
    @docker cp ./database/mysql_jobmgmt_ddl.sql {{PROJECT_NAME}}-mysql:/tmp
    @docker cp ./database/mysql_jobmgmt_lov_load.sql {{PROJECT_NAME}}-mysql:/tmp
    @docker cp ./database/mysql_jobmgmt_data_load.sql {{PROJECT_NAME}}-mysql:/tmp
    @docker exec {{PROJECT_NAME}}-mysql chmod +x /tmp/createUserDB.sh
    @echo "Creating {{PROJECT_NAME}} db for {{PROJECT_NAME}}..."
    @docker exec {{PROJECT_NAME}}-mysql /tmp/createUserDB.sh
    @echo "Creating tables for {{PROJECT_NAME}}..."
    @docker exec {{PROJECT_NAME}}-mysql /tmp/loadDB.sh {{PROJECT_NAME}} {{PROJECT_NAME}} localhost /tmp/
