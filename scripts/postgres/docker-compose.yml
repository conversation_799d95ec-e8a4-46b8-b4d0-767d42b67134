version: '3.1'

services:

  postgres:
    container_name: "${PROJECT_NAME}-pgsql"
    image: postgres:${PGSQL_TAG}
    restart: always
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: sonarqube
    ports:
      - "5432:5432"

  pgadmin:
    container_name: pgadmin4_container
    image: dpage/pgadmin4
    restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: postgres
      PGADMIN_CONFIG_MAX_LOGIN_ATTEMPTS: 0
    ports:
      - "5051:80"

  sonarqube:
    container_name: sonarqube
    image: sonarqube:community
    restart: always
    depends_on:
      - postgres
    ports:
      - "9000:9000"
    environment:
      SONAR_JDBC_URL: *****************************************************
      SONAR_JDBC_USERNAME: postgres
      SONAR_JDBC_PASSWORD: postgres
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_extensions:/opt/sonarqube/extensions

  sonar-scanner:
    image: sonarsource/sonar-scanner-cli
    container_name: sonar-scanner
    depends_on:
      - sonarqube
    environment:
      - SONAR_HOST_URL=http://sonarqube:9000
      - SONAR_TOKEN=${SONAR_TOKEN}
    volumes:
      - ${USERPROFILE}/IdeaProjects/${PROJECT_NAME}:/usr/src
      - ${USERPROFILE}/IdeaProjects/${CORE_PROJECT}:/usr/src
      - ./shared:/shared
volumes:
  sonarqube_data:
  sonarqube_extensions:
