-- Officer, Detective, Prosecutor, etc.
CREATE TABLE roles_lov (
    code VARCHAR(15) NOT NULL PRIMARY KEY,
    description VARCHAR(2047) NOT NULL,
    deprecated BOOLEAN NOT NULL DEFAULT false
);

-- A case can have only one status at a time.
CREATE TABLE case_status_lov (
  code VARCHAR(8) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

-- Robbery, murder, assault, etc.
CREATE TABLE case_type_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE audit_action_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE audit_entity_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE notification_type_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE system_theme_lov (
  code INTEGER NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

-- CREATE TABLE system_theme_lov (
--   code VARCHAR(15) NOT NULL PRIMARY KEY,
--   description VARCHAR(2047) NOT NULL,
--   deprecated BOOLEAN NOT NULL DEFAULT false
-- );

CREATE TABLE system_files_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  file BYTEA NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE data_set_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  display_order INTEGER NOT NULL,
  description VARCHAR(2047) NOT NULL,
  java_class_name VARCHAR(255) NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE uploaded_data_status_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

-- --Records with a same task_type_lov_id should match with a java object (entity)
-- CREATE TABLE task_type_lov_definition (
--   id SERIAL NOT NULL PRIMARY KEY,
--   task_type_lov_id VARCHAR(15) NOT NULL,
--   field VARCHAR(50) NOT NULL PRIMARY KEY
-- );

CREATE TABLE workflow_type_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  data JSONB NOT NULL,
  deprecated  BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE demo_lov (
  code VARCHAR(15) NOT NULL PRIMARY KEY,
  description VARCHAR(2047) NOT NULL,
  deprecated BOOLEAN NOT NULL DEFAULT false
);

CREATE TABLE hierarchy_level (
  level INTEGER NOT NULL PRIMARY KEY,
  hierarchy_name VARCHAR(25) UNIQUE NOT NULL,
  description VARCHAR(255) NOT NULL
);
--Organization level cannot be deleted.
CREATE RULE lock_organization_level
  AS ON DELETE TO hierarchy_level
  WHERE level=0 DO INSTEAD NOTHING;

CREATE TABLE hierarchy_node (
  node_id INTEGER NOT NULL PRIMARY KEY,
  parent_id INTEGER NULL,
  level INTEGER NOT NULL,
  hierarchy_value VARCHAR(25) NOT NULL,
  CONSTRAINT fk_level
    FOREIGN KEY (level) REFERENCES hierarchy_level (level)
);

--Organization level cannot be deleted.
CREATE RULE lock_organization_node
  AS ON DELETE TO hierarchy_node
  WHERE node_id=0 DO INSTEAD NOTHING;

CREATE TABLE system (
  id VARCHAR(15) NOT NULL PRIMARY KEY,
  theme INTEGER NULL,
  colors VARCHAR(32) NULL,
  logo TEXT NULL,
  CONSTRAINT system_theme_lov
    FOREIGN KEY (theme) REFERENCES system_theme_lov (code)
);

CREATE TABLE users (
  user_id UUID PRIMARY KEY,
  email VARCHAR(320) NOT NULL UNIQUE,
  is_active BOOLEAN NOT NULL DEFAULT false,
  first_name VARCHAR(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  profile_image BYTEA NULL,
  title VARCHAR(15) NULL,
  hierarchy_id INTEGER NULL,
  effective_date TIMESTAMP NOT NULL,
  termination_date TIMESTAMP NULL,
  CONSTRAINT fk_hierarchy
    FOREIGN KEY (hierarchy_id) REFERENCES hierarchy_node (node_id) ON DELETE CASCADE,
  CONSTRAINT ck_users_termination_date
     CHECK (termination_date IS NULL OR termination_date >= effective_date)
);

CREATE TABLE role (
  role_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(25) NOT NULL UNIQUE,
  department VARCHAR(15) NOT NULL,
  superior_id UUID NULL,
  description VARCHAR(2047) NOT NULL
--                   ,
--   CONSTRAINT pk_role UNIQUE (name)
);

-- This table allows a user to have more than one role.
CREATE TABLE user_roles_xref (
  user_id UUID NOT NULL,
  role_id UUID NOT NULL,
  CONSTRAINT pk_user_roles_xref PRIMARY KEY (user_id, role_id),
  CONSTRAINT fk_users
      FOREIGN KEY (user_id) REFERENCES users (user_id) ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT fk_roles
      FOREIGN KEY (role_id) REFERENCES role (role_id) ON DELETE CASCADE
);

CREATE TABLE role_capabilities_xref (
   role_id UUID NOT NULL,
   component VARCHAR(2047) NOT NULL,
   view BOOLEAN NOT NULL,
   edit BOOLEAN NOT NULL,
   "create" BOOLEAN NOT NULL,
   delete BOOLEAN NOT NULL,
   CONSTRAINT pk_role_capabilities_xref PRIMARY KEY (role_id, component),
   CONSTRAINT fk_role
       FOREIGN KEY (role_id) REFERENCES role (role_id) ON DELETE CASCADE
);

-- Need table(s) for user email, phone, etc. plus xref.
CREATE TABLE user_contact_xref (
  user_id UUID NOT NULL PRIMARY KEY,
  phone VARCHAR(15) NULL UNIQUE,
  mobile VARCHAR(15) NULL UNIQUE,
  address VARCHAR(95) NULL,
  CONSTRAINT fk_users
    FOREIGN KEY (user_id) REFERENCES users (user_id)
);

CREATE TABLE user_billing_information_xref (
    user_id UUID PRIMARY KEY NOT NULL,
    stripe_customer_id VARCHAR(30) UNIQUE NOT NULL,
   CONSTRAINT fk_users
     FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE
);

CREATE TABLE user_credit_card (
   stripe_card_id VARCHAR(40) PRIMARY KEY NOT NULL,
   stripe_customer_id VARCHAR(30) NOT NULL,
   CONSTRAINT fk_user_billing_information_xref
     FOREIGN KEY (stripe_customer_id) REFERENCES user_billing_information_xref (stripe_customer_id) ON DELETE CASCADE
);

CREATE TABLE user_config (
  user_id UUID NOT NULL PRIMARY KEY,
  dashboard JSONB NULL,
  CONSTRAINT fk_users
   FOREIGN KEY (user_id) REFERENCES users (user_id)
);

CREATE SEQUENCE notification_id_seq;
CREATE TABLE user_notifications_xref (
 id INTEGER DEFAULT NEXTVAL('notification_id_seq') NOT NULL,
 user_id UUID NOT NULL,
 type VARCHAR(15) NOT NULL,
 body TEXT,
 url VARCHAR(2047),
 created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
 CONSTRAINT pk_user_notification_xref PRIMARY KEY (user_id, id),
 CONSTRAINT fk_notification_type_lov
     FOREIGN KEY (type) REFERENCES notification_type_lov (code)
);

CREATE TABLE user_notifications_config_xref (
 user_id UUID NOT NULL,
 type VARCHAR(15) NOT NULL,
 object_id UUID NOT NULL,
 notify BOOLEAN NOT NULL,
 CONSTRAINT pk_user_notification_config_xref PRIMARY KEY (user_id, type),
 CONSTRAINT fk_users
     FOREIGN KEY (user_id) REFERENCES users (user_id),
 CONSTRAINT fk_notification_type_lov
     FOREIGN KEY (type) REFERENCES notification_type_lov (code)
);

CREATE TABLE case_data_information (
  case_id UUID NOT NULL PRIMARY KEY,
  region VARCHAR(100) NULL,
  contact_type VARCHAR(100) NULL,
  contract_approval VARCHAR(100) NULL,
  contract VARCHAR(100) NULL,
  omnia VARCHAR(100) NULL,
  margin_override_requested VARCHAR(100) NULL,
  tax_exempt VARCHAR(100) NULL,
  prevailing_wage DOUBLE PRECISION NULL,
  credit DOUBLE PRECISION NULL,
  bond DOUBLE PRECISION NULL,
  engineering VARCHAR(100) NULL,
  badging_required VARCHAR(100) NULL,
  city VARCHAR(100) NULL,
  operation_review VARCHAR(100) NULL,
  estimated_cost DOUBLE PRECISION NULL,
  bid_outcome VARCHAR(100) NULL,
  proposal_sent VARCHAR(100) NULL,
  proposal_sent_date VARCHAR(100) NULL
);

-- CREATE TABLE case_workflow (
--   case_id UUID NOT NULL,
--   workflow_id VARCHAR(15) NOT NULL,
--   CONSTRAINT pk_case_type_xref PRIMARY KEY (case_id, case_type),
--   CONSTRAINT fk_case_data
--    FOREIGN KEY (case_id) REFERENCES case_data (case_id),
--   CONSTRAINT fk_case_type
--    FOREIGN KEY (case_type) REFERENCES case_type_lov (code)
-- );

-- Bare bones case data.
CREATE TABLE case_data (
  case_id UUID PRIMARY KEY,
  name VARCHAR(25) NOT NULL UNIQUE,
  hierarchy_id INTEGER NOT NULL,
  opened_by UUID NOT NULL,
  opened_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  description VARCHAR(2047),
  location VARCHAR(90),
  current_status VARCHAR(15) NOT NULL DEFAULT 'Open',
  status_modified_by UUID NOT NULL,
  status_update_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_case_data_opened
    FOREIGN KEY (opened_by) REFERENCES users (user_id) ON DELETE CASCADE,
  CONSTRAINT fk_case_data_modified
    FOREIGN KEY (status_modified_by) REFERENCES users (user_id) ON DELETE CASCADE,
  CONSTRAINT fk_case_status_lov
    FOREIGN KEY (current_status) REFERENCES case_status_lov (code),
  CONSTRAINT fk_hierarchy
    FOREIGN KEY (hierarchy_id) REFERENCES hierarchy_node (node_id),
  CONSTRAINT fk_data_information
    FOREIGN KEY (case_id) REFERENCES case_data_information (case_id)
);

-- State machine transitions.
CREATE TABLE case_status_transition (
  id INTEGER NOT NULL PRIMARY KEY,
  source VARCHAR(8) NOT NULL,
  target VARCHAR(8) NOT NULL,
  CONSTRAINT fk_case_status_source
      FOREIGN KEY (source) REFERENCES case_status_lov (code),
  CONSTRAINT fk_case_status_target
      FOREIGN KEY (target) REFERENCES case_status_lov (code)
);

-- This table allows a case to have more than one type (e.g. robbery and assault).
CREATE TABLE case_type_xref (
  case_id UUID NOT NULL,
  case_type VARCHAR(15) NOT NULL,
  CONSTRAINT pk_case_type_xref PRIMARY KEY (case_id, case_type),
  CONSTRAINT fk_case_data
    FOREIGN KEY (case_id) REFERENCES case_data (case_id),
  CONSTRAINT fk_case_type
    FOREIGN KEY (case_type) REFERENCES case_type_lov (code)
);

CREATE TABLE case_status_history (
   id SERIAL NOT NULL PRIMARY KEY,
   case_id UUID NOT NULL,
   status VARCHAR(15) NOT NULL,
   status_update_date TIMESTAMP NOT NULL
);

CREATE TABLE uploaded_data (
  data_set_code VARCHAR(15) NOT NULL PRIMARY KEY,
  file_name VARCHAR(2047) NULL,
  status_code VARCHAR(15) NOT NULL,
  uploaded_date TIMESTAMP NULL,
  CONSTRAINT fk_uploaded_data_code
    FOREIGN KEY (data_set_code) REFERENCES data_set_lov (code),
  CONSTRAINT fk_uploaded_data_status
    FOREIGN KEY (status_code) REFERENCES uploaded_data_status_lov (code)
);

-- Need a table for witnesses.

-- Need a table for document attachments.

-- Need history tables to track changes to primary tables.


-- Log table

CREATE TABLE service_call_log (
  id UUID NOT NULL PRIMARY KEY,
  accept_type text,
  class_name text,
  client_id integer,
  content_type text,
  elapsed_time_ms interval,
  end_time timestamp without time zone,
  error_msg text,
  host_name text,
  http_action text,
  log_response boolean NOT NULL,
  method_name text,
  path_parms json,
  query_parms json,
  remote_ip text,
  req_header_parms json,
  resource text,
  resp_header_parms json,
  response_code integer NOT NULL,
  request_body text,
  response_body text,
  stack_trace text,
  start_time timestamp without time zone,
  submit_time timestamp without time zone,
  user_name text
);

CREATE TABLE audit_log (
   audit_id UUID NOT NULL PRIMARY KEY,
   user_id UUID NOT NULL,
   action_code VARCHAR(15) NOT NULL,
   entity_code TEXT NOT NULL,
   entity_key TEXT NOT NULL,
   audit_date TIMESTAMP NOT NULL,
   entity_value JSON NULL
);

CREATE TABLE workflow_type (
 id UUID NOT NULL PRIMARY KEY,
 name VARCHAR(25) NOT NULL UNIQUE,
 description VARCHAR(2047) NULL,
 entity VARCHAR(15) NOT NULL,
 deletable BOOLEAN DEFAULT TRUE,
 created_by_system BOOLEAN DEFAULT FALSE,
 CONSTRAINT fk_workflow_type_lov
   FOREIGN KEY (entity) REFERENCES workflow_type_lov (code)
);

CREATE TABLE workflow (
  id UUID NOT NULL PRIMARY KEY,
  workflow_type_id UUID NOT NULL,
  version INTEGER NULL,
  active BOOLEAN DEFAULT FALSE,
  completed BOOLEAN DEFAULT FALSE,
  CONSTRAINT fk_workflow
    FOREIGN KEY (workflow_type_id) REFERENCES workflow_type (id) ON DELETE CASCADE,
  CONSTRAINT unique_type_version UNIQUE (workflow_type_id, version)
);

CREATE TABLE workflow_task_type (
  task_id UUID NOT NULL PRIMARY KEY,
  workflow_id UUID NOT NULL,
  task_name VARCHAR(100) NOT NULL,
  fields JSONB NULL,
  approval_task BOOLEAN NULL,
  has_email BOOLEAN NOT NULL DEFAULT FALSE,
  email_to TEXT[] NULL,
  email_subject VARCHAR(60) NULL,
  email_body VARCHAR(10000) NULL,
  trigger_workflow BOOLEAN NOT NULL DEFAULT FALSE,
  trigger_workflow_id UUID NULL,
  has_directory BOOLEAN NOT NULL DEFAULT FALSE,
  service_level_agreement INTEGER NOT NULL DEFAULT 3,
  service_level_agreement_warning INTEGER NOT NULL DEFAULT 2,
  description VARCHAR(2047) NULL,
  completed BOOLEAN NOT NULL DEFAULT FALSE,
  CONSTRAINT unique_task_name UNIQUE (workflow_id, task_name),
--     FOREIGN KEY (workflow_id) REFERENCES workflow (id),
  CONSTRAINT fk_workflow
    FOREIGN KEY (workflow_id) REFERENCES workflow (id),
  CONSTRAINT fk_workflow_to_trigger
    FOREIGN KEY (trigger_workflow_id) REFERENCES workflow_type (id)
--                                 ,
--   CONSTRAINT fk_task_type
--     FOREIGN KEY (task_type_id) REFERENCES workflow_type_lov (code)
);

CREATE TABLE workflow_task (
 id UUID NOT NULL PRIMARY KEY,
 task_type_id UUID NOT NULL,
 value JSONB NULL,
 req_value JSONB NOT NULL,
 seq INTEGER NULL,
 done BOOLEAN NOT NULL,
 approved BOOLEAN NULL,
 deleted BOOLEAN NULL,
 workflow_completed BOOLEAN NULL,
 entity_name VARCHAR(50) NOT NULL,
 justification VARCHAR(2047) NULL,
 worked_by UUID NULL,
 completed_date TIMESTAMP NULL,
 created_date TIMESTAMP NOT NULL,
 modified_date TIMESTAMP NULL,
 modified_by UUID NULL,
 created_by UUID NOT NULL,
 CONSTRAINT fk_workflow_task_type
   FOREIGN KEY (task_type_id) REFERENCES workflow_task_type (task_id),
 CONSTRAINT fk_users
   FOREIGN KEY (worked_by) REFERENCES users (user_id)
);

CREATE TABLE role_workflow_task_xref (
  role_id UUID NOT NULL,
  task_id UUID NOT NULL,
  CONSTRAINT pk_role_task_xref PRIMARY KEY (role_id, task_id),
  CONSTRAINT fk_roles
   FOREIGN KEY (role_id) REFERENCES role (role_id),
  CONSTRAINT fk_workflow_task_info
   FOREIGN KEY (task_id) REFERENCES workflow_task_type (task_id) ON DELETE CASCADE
);

-- Workflow task relations.
CREATE TABLE workflow_task_type_relation (
  id INTEGER NOT NULL,
  workflow_id UUID NOT NULL,
  input UUID NOT NULL,
  output UUID NOT NULL,
  priority INTEGER NOT NULL,
  relevant_data_field VARCHAR(50) NULL,
  relevant_data_value VARCHAR(50) NULL,
  comparison_operator VARCHAR(2) NULL,
  CONSTRAINT pk_role_task_type_xref PRIMARY KEY (id, workflow_id),
  CONSTRAINT fk_workflow
   FOREIGN KEY (workflow_id) REFERENCES workflow(id),
  CONSTRAINT fk_workflow_task_input
   FOREIGN KEY (input) REFERENCES workflow_task_type (task_id) ON DELETE CASCADE,
  CONSTRAINT fk_workflow_task_output
   FOREIGN KEY (output) REFERENCES workflow_task_type (task_id) ON DELETE CASCADE
);

