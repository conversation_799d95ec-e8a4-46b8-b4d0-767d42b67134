#/bin/bash
# usage: CreateDB.sh <pgHost> <dbToCreate> <ownerOfdb>
host=$1
dbToCreate=$2
dbOwner=$3

sqlString="DROP DATABASE IF EXISTS $dbToCreate WITH (FORCE);"

psql -U postgres --dbname=postgres --host=$host --quiet <<-EOSQL
       $sqlString
EOSQL

sqlString="CREATE DATABASE $dbToCreate; ALTER DATABASE $dbToCreate OWNER TO $dbOwner; GRANT ALL PRIVILEGES ON DATABASE $dbToCreate TO $dbOwner;"
psql -U postgres --dbname=postgres --host=$host --quiet <<-EOSQL
	    $sqlString
EOSQL