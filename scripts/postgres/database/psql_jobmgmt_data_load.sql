INSERT INTO system (id, theme, colors, logo) VALUES
('system-config', NULL, NULL, NULL);

INSERT INTO hierarchy_level (level, hierarchy_name, description) VALUES
  (0, 'Organization', 'Organization Description.'),
  (1, 'Division', 'Division Description.'),
  (2, 'Facility', 'Facility Description.');

INSERT INTO hierarchy_node (node_id, parent_id, level, hierarchy_value) VALUES
  (0, NULL, 0, 'Department Of Justice'),
  (1, 0, 1, 'Eastern'),
  (2, 0, 1, 'Central'),
  (3, 0, 1, 'Western'),
  (4, 1, 2, 'A'),
  (5, 1, 2, 'B'),
  (6, 2, 2, 'C'),
  (7, 3, 2, 'D'),
  (8, 3, 2, 'E');

INSERT INTO case_status_transition (id, source, target) VALUES
  (1, 'open', 'close'),
  (2, 'close', 'open'),
  (3, 'open', 'cold'),
  (4, 'cold', 'open'),
  (5, 'close', 'archive');

INSERT INTO users (user_id, email, is_active, first_name, last_name, title, hierarchy_id, effective_date, termination_date) VALUES
('5e16afd9-0b44-4e97-a1e8-3dcc1afa4beb', '<EMAIL>', TRUE, 'Sam', 'Wooster', 'M.D.', 0, '2012-06-18', NULL),
('9fdaed10-dcc2-45fd-98e7-677429b413d9', '<EMAIL>', TRUE, 'Eliezer', 'Morillo', '', 0, '2012-06-18', NULL),
('2d8b089a-5906-4da5-aa3c-2450a1a32530', '<EMAIL>', TRUE, 'Dorsey', 'Test', 'M.D.', 0, '2012-06-18', NULL);

INSERT INTO role (role_id, name, department, superior_id, description) VALUES
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'Admin', 'Administration', null, 'System manager'),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'Sales Associate', 'Sales', null, 'Welcome customers on arrival at a retailer'),
('038a99d7-acbf-4713-b17d-79ac55685453', 'Sales Manager', 'Sales', null, 'Provides leadership to the sales team'),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'Operations Manager', 'Operations', null, 'Implement and maintain organization processes'),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'CEO', 'Administration', null, 'Manage company'),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'Visitor', 'Unknown', null, 'Unknown');

INSERT INTO user_roles_xref (user_id, role_id) VALUES
('5e16afd9-0b44-4e97-a1e8-3dcc1afa4beb', 'd39091a9-fe2f-4952-9bb3-d760ad06a1bd'),
('9fdaed10-dcc2-45fd-98e7-677429b413d9', 'd39091a9-fe2f-4952-9bb3-d760ad06a1bd'),
('2d8b089a-5906-4da5-aa3c-2450a1a32530', '2a97fbe1-487e-4bce-9fc6-5581ce2508ad');

INSERT INTO role_capabilities_xref (role_id, component, view, edit, "create", delete) VALUES
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,user-list', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,role-list', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,role-list,details', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,audit-log', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,demo', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,system', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,load-data', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,cases', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,cases,:case', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,workflow', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,workflow,manage-workflow', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,workflow,manage-workflow,:workflow', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,workflow,manage-workflow,:workflow,:task', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,workflow,workflow-history', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,organization', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,state-management', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'admin,state-management,jobs', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'home', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'home,dashboard', true, true, true, true),
('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'home,workflow', true, true, true, true),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,user-list', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,role-list', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,role-list,details', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,audit-log', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,demo', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,system', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,load-data', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,cases', true, true, true, true),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,cases,:case', true, true, true, true),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,workflow', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,workflow,manage-workflow', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,workflow,manage-workflow,:workflow', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,workflow,manage-workflow,:workflow,:task', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,workflow,workflow-history', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,organization', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,state-management', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'admin,state-management,jobs', false, false, false, false),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'home', true, true, true, true),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'home,dashboard', true, true, true, true),
('2a97fbe1-487e-4bce-9fc6-5581ce2508ad', 'home,workflow', true, true, true, true),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,user-list', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,role-list', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,role-list,details', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,audit-log', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,demo', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,system', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,load-data', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,cases', true, true, true, true),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,cases,:case', true, true, true, true),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,workflow', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,workflow,manage-workflow', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,workflow,manage-workflow,:workflow', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,workflow,manage-workflow,:workflow,:task', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,workflow,workflow-history', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,organization', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,state-management', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'admin,state-management,jobs', false, false, false, false),
('038a99d7-acbf-4713-b17d-79ac55685453', 'home', true, true, true, true),
('038a99d7-acbf-4713-b17d-79ac55685453', 'home,dashboard', true, true, true, true),
('038a99d7-acbf-4713-b17d-79ac55685453', 'home,workflow', true, true, true, true),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,user-list', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,role-list', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,role-list,details', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,audit-log', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,demo', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,system', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,load-data', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,cases', true, true, true, true),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,cases,:case', true, true, true, true),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,workflow', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,workflow,manage-workflow', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,workflow,manage-workflow,:workflow', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,workflow,manage-workflow,:workflow,:task', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,workflow,workflow-history', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,organization', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,state-management', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'admin,state-management,jobs', false, false, false, false),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'home', true, true, true, true),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'home,dashboard', true, true, true, true),
('7e33e8c1-9488-4535-9061-d877364c24b2', 'home,workflow', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,user-list', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,role-list', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,role-list,details', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,audit-log', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,demo', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,system', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,load-data', false, false, false, false),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,cases', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,cases,:case', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,workflow', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,workflow,manage-workflow', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,workflow,manage-workflow,:workflow', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,workflow,manage-workflow,:workflow,:task', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,workflow,workflow-history', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,organization', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,state-management', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'admin,state-management,jobs', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'home', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'home,dashboard', true, true, true, true),
('a3a1f14b-49fe-4d8f-b338-d800d9de75fe', 'home,workflow', true, true, true, true),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,user-list', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,role-list', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,role-list,details', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,audit-log', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,demo', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,system', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,load-data', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,cases', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,cases,:case', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,workflow', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,workflow,manage-workflow', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,workflow,manage-workflow,:workflow', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,workflow,manage-workflow,:workflow,:task', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,workflow,workflow-history', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,organization', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,state-management', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'admin,state-management,jobs', false, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'home', true, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'home,dashboard', true, false, false, false),
('76c90a96-10fa-4776-b1a2-0ba9887d2bf1', 'home,workflow', true, false, false, false);

INSERT INTO uploaded_data (data_set_code, file_name, status_code, uploaded_date) VALUES
  ( 'SMC', NULL, 'N', NULL),
  ( 'W', NULL, 'N', NULL),
  ( 'OH', NULL, 'N', NULL),
  ( 'C', NULL, 'N', NULL),
  ( 'R', NULL, 'N', NULL),
  ( 'U', NULL, 'N', NULL);

INSERT INTO workflow_type (id, name, description, entity, created_by_system) VALUES
  ('e451fcc5-31b9-4391-9afa-325c045eb2ec', 'User Role Request', 'User Role Request', 'URR', true),
  ('29b0b4cd-3eb4-4d74-8000-1e600da043fe', 'User Hierarchy Change', 'User Hierarchy Change', 'UHC', true);

INSERT INTO workflow (id, workflow_type_id, version, active, completed) VALUES
   ('6dff2664-3216-463c-9451-009791a41c68', 'e451fcc5-31b9-4391-9afa-325c045eb2ec', 1, true, false),
   ('02d25f3b-5758-4e91-bc3d-c34eda3d4c4f', '29b0b4cd-3eb4-4d74-8000-1e600da043fe', 1, true, false);

INSERT INTO workflow_task_type (task_id, workflow_id, task_name, service_level_agreement, service_level_agreement_warning, description) VALUES
  ('9aba5847-c986-4cd7-9037-e7ada1634673', '6dff2664-3216-463c-9451-009791a41c68', 'User Role Change', 3, 2, 'User Role Change'),
  ('da648d52-67a6-4937-9119-522abc09ef65', '02d25f3b-5758-4e91-bc3d-c34eda3d4c4f', 'User Hierarchy Change',  3, 2, 'User Hierarchy Change');

INSERT INTO role_workflow_task_xref (role_id, task_id) VALUES
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', '9aba5847-c986-4cd7-9037-e7ada1634673'),
  ('d39091a9-fe2f-4952-9bb3-d760ad06a1bd', 'da648d52-67a6-4937-9119-522abc09ef65');
