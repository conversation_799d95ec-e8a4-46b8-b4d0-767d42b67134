#!/bin/bash

set -e

psql --username postgres --dbname postgres -tAc "SELECT 1 FROM pg_roles WHERE rolname='jas'" | \
grep -q 1 || \
psql -v ON_ERROR_STOP=1 --username postgres --dbname postgres <<-EOSQL
      CREATE USER jas WITH ENCRYPTED PASSWORD 'Good2No!';
      ALTER USER jas CREATEDB;
EOSQL

psql -v ON_ERROR_STOP=1 --username postgres --dbname postgres <<-EOSQL
      DROP DATABASE IF EXISTS jas WITH (FORCE);
EOSQL

psql -v ON_ERROR_STOP=1 --username postgres --dbname postgres <<-EOSQL
	    CREATE DATABASE jas;
	    ALTER DATABASE jas OWNER TO jas;
	    GRANT ALL PRIVILEGES ON DATABASE jas TO jas;
EOSQL
